<?php
ob_start();
error_reporting(0);
define('API_KEY','8155986826:AAGSIj-HFXAKhC4ikXjfHEVRgyIFFtU9lME');

// Include group game functions (3-player only)
include_once 'group_game.php';
//-----------------------------------------------------------------------------------------
//فانکشن jijibot :
function jijibot($method,$datas=[]){
    $url = "https://api.telegram.org/bot".API_KEY."/".$method;
    $ch = curl_init();
    curl_setopt($ch,CURLOPT_URL,$url);
    curl_setopt($ch,CURLOPT_RETURNTRANSFER,true);
    curl_setopt($ch,CURLOPT_POSTFIELDS,$datas);
    $res = curl_exec($ch);
    if(curl_error($ch)){
        var_dump(curl_error($ch));
    }else{
        return json_decode($res);
    }
}

//-----------------------------------------------------------------------------------------
//تابع جوین اجباری :
function force_join($chat_id, $first_name, $usernamebot) {
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"سلام $first_name 👋

برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔔 عضویت در کانال دوم",'url'=>"https://t.me/speedxteam"],
                    ['text'=>"🔔 عضویت در کانال اول",'url'=>"https://t.me/speedx_bots"]
                ],
                [
                    ['text'=>"🔄 بررسی مجدد عضویت",'callback_data'=>"check_membership"]
                ]
            ]
        ])
    ]);
}

//-----------------------------------------------------------------------------------------
//تابع تولید توکن کاربری:
function generateUserToken() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    $token = '';
    for ($i = 0; $i < 7; $i++) {
        $token .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $token;
}

//تابع بررسی یکتا بودن توکن:
function isTokenUnique($token) {
    $userDir = "data/user/";
    if (!is_dir($userDir)) {
        return true;
    }

    $tokenLower = strtolower($token); // تبدیل به حروف کوچک برای مقایسه
    $files = scandir($userDir);
    foreach ($files as $file) {
        if ($file != "." && $file != ".." && pathinfo($file, PATHINFO_EXTENSION) == "json") {
            $userData = json_decode(file_get_contents($userDir . $file), true);
            if (isset($userData['userfild']['user_token']) && strtolower($userData['userfild']['user_token']) == $tokenLower) {
                return false;
            }
        }
    }
    return true;
}

//تابع تولید توکن یکتا:
function generateUniqueUserToken() {
    do {
        $token = generateUserToken();
    } while (!isTokenUnique($token));
    return $token;
}

//تابع تولید کد رندوم 7 کاراکتری برای لینک بازی:
function generateRandomGameCode() {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < 7; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $code;
}

//تابع ذخیره کد بازی:
function saveGameCode($userId, $gameCode) {
    $gameCodesFile = "data/game_codes.json";
    $gameCodes = [];

    if (file_exists($gameCodesFile)) {
        $gameCodes = json_decode(file_get_contents($gameCodesFile), true);
        if (!$gameCodes) $gameCodes = [];
    }

    $gameCodes[$gameCode] = [
        'user_id' => $userId,
        'created_at' => time(),
        'expires_at' => time() + (24 * 60 * 60) // 24 ساعت اعتبار
    ];

    file_put_contents($gameCodesFile, json_encode($gameCodes, JSON_PRETTY_PRINT));
}

//تابع یافتن کاربر با کد بازی:
function findUserByGameCode($gameCode) {
    $gameCodesFile = "data/game_codes.json";

    if (!file_exists($gameCodesFile)) {
        return null;
    }

    $gameCodes = json_decode(file_get_contents($gameCodesFile), true);
    if (!$gameCodes || !isset($gameCodes[$gameCode])) {
        return null;
    }

    $codeData = $gameCodes[$gameCode];

    // بررسی انقضای کد
    if ($codeData['expires_at'] < time()) {
        // حذف کد منقضی شده
        unset($gameCodes[$gameCode]);
        file_put_contents($gameCodesFile, json_encode($gameCodes, JSON_PRETTY_PRINT));
        return null;
    }

    return $codeData['user_id'];
}

//تابع یافتن کاربر با توکن:
function findUserByToken($token) {
    $userDir = "data/user/";
    if (!is_dir($userDir)) {
        return null;
    }

    $tokenLower = strtolower($token); // تبدیل به حروف کوچک برای مقایسه
    $files = scandir($userDir);
    foreach ($files as $file) {
        if ($file != "." && $file != ".." && pathinfo($file, PATHINFO_EXTENSION) == "json") {
            $userData = json_decode(file_get_contents($userDir . $file), true);
            if (isset($userData['userfild']['user_token']) && strtolower($userData['userfild']['user_token']) == $tokenLower) {
                $userId = pathinfo($file, PATHINFO_FILENAME);
                return ['user_id' => $userId, 'data' => $userData];
            }
        }
    }
    return null;
}

//-----------------------------------------------------------------------------------------
//تابع محاسبه و نمایش آخرین بازدید:
function getLastVisitStatus($user_data) {
    if (!isset($user_data['userfild']['last_visit_timestamp'])) {
        return "نامشخص";
    }

    $lastVisitTime = $user_data['userfild']['last_visit_timestamp'];
    $currentTime = time();
    $timeDiff = $currentTime - $lastVisitTime;

    // اگر کمتر از 5 دقیقه پیش فعالیت داشته، آنلاین است
    if ($timeDiff <= 300) { // 300 ثانیه = 5 دقیقه
        return "آنلاین";
    }

    // محاسبه زمان گذشته
    $minutes = floor($timeDiff / 60);
    $hours = floor($timeDiff / 3600);
    $days = floor($timeDiff / 86400);

    if ($days > 0) {
        return $days . " روز پیش";
    } elseif ($hours > 0) {
        return $hours . " ساعت پیش";
    } elseif ($minutes > 0) {
        return $minutes . " دقیقه پیش";
    } else {
        return "آنلاین";
    }
}

//تابع به‌روزرسانی آخرین بازدید:
function updateLastVisit($user_id) {
    $userFile = "data/user/$user_id.json";
    if (file_exists($userFile)) {
        $userData = json_decode(file_get_contents($userFile), true);
        $userData['userfild']['last_visit_timestamp'] = time();
        file_put_contents($userFile, json_encode($userData, true));
    }
}

//-----------------------------------------------------------------------------------------
//تابع محاسبه زمان باقی‌مانده اشتراک:
function getSubscriptionInfo($user_data) {
    if (!isset($user_data['userfild']['subscription']) ||
        !isset($user_data['userfild']['subscription']['is_active']) ||
        !$user_data['userfild']['subscription']['is_active'] ||
        !isset($user_data['userfild']['subscription']['end_date'])) {
        return [
            'type' => 'عادی',
            'remaining' => 'دائمی'
        ];
    }

    $subscription = $user_data['userfild']['subscription'];
    $now = time();
    $end_date = $subscription['end_date'];

    // اگر اشتراک منقضی شده
    if ($end_date <= $now) {
        return [
            'type' => 'عادی',
            'remaining' => 'دائمی'
        ];
    }

    // محاسبه زمان باقی‌مانده
    $remaining_seconds = $end_date - $now;
    $days = floor($remaining_seconds / (24 * 60 * 60));
    $hours = floor(($remaining_seconds % (24 * 60 * 60)) / (60 * 60));
    $minutes = floor(($remaining_seconds % (60 * 60)) / 60);

    // تعیین نوع اشتراک
    $subscription_type = 'ویژه';
    switch($subscription['type']) {
        case 'sub_30':
            $subscription_type = 'ویژه 30 روزه';
            break;
        case 'sub_60':
            $subscription_type = 'ویژه 60 روزه';
            break;
        case 'sub_90':
            $subscription_type = 'ویژه 90 روزه';
            break;
        case 'sub_365':
            $subscription_type = 'ویژه 1 ساله';
            break;
    }

    // فرمت کردن زمان باقی‌مانده
    $remaining_text = '';
    if ($days > 0) {
        $remaining_text .= $days . ' روز';
    }
    if ($hours > 0) {
        if ($remaining_text != '') $remaining_text .= ' و ';
        $remaining_text .= $hours . 'ساعت';
    }
    if ($minutes > 0) {
        if ($remaining_text != '') $remaining_text .= ' و ';
        $remaining_text .= $minutes . 'دقیقه';
    }

    // اگر کمتر از یک دقیقه باقی مانده
    if ($remaining_text == '') {
        $remaining_text = 'کمتر از یک دقیقه';
    }

    return [
        'type' => $subscription_type,
        'remaining' => $remaining_text
    ];
}

//-----------------------------------------------------------------------------------------
//کلاس مدیریت Session برای تایید شماره تلفن:
class Session {
    public static function set($user_id, $key, $value) {
        $session_file = "data/session/$user_id.json";

        // ایجاد پوشه session در صورت عدم وجود
        if (!file_exists("data/session")) {
            mkdir("data/session", 0777, true);
        }

        $session_data = [];
        if (file_exists($session_file)) {
            $session_data = json_decode(file_get_contents($session_file), true);
        }

        $session_data[$key] = $value;
        file_put_contents($session_file, json_encode($session_data, true));
    }

    public static function get($user_id, $key) {
        $session_file = "data/session/$user_id.json";

        if (!file_exists($session_file)) {
            return null;
        }

        $session_data = json_decode(file_get_contents($session_file), true);
        return isset($session_data[$key]) ? $session_data[$key] : null;
    }

    public static function delete($user_id, $key) {
        $session_file = "data/session/$user_id.json";

        if (!file_exists($session_file)) {
            return;
        }

        $session_data = json_decode(file_get_contents($session_file), true);
        unset($session_data[$key]);
        file_put_contents($session_file, json_encode($session_data, true));
    }
}

//-----------------------------------------------------------------------------------------
//تابع بررسی عضویت در دو کانال :
function check_membership($user_id, $channel1, $channel2) {
    $forchannel1 = jijibot('getChatMember',['chat_id'=>"@$channel1",'user_id'=>"$user_id"]);
    $tch1 = $forchannel1->result->status;

    $forchannel2 = jijibot('getChatMember',['chat_id'=>"@$channel2",'user_id'=>"$user_id"]);
    $tch2 = $forchannel2->result->status;

    return (($tch1 == 'member' || $tch1 == 'creator' || $tch1 == 'administrator') &&
            ($tch2 == 'member' || $tch2 == 'creator' || $tch2 == 'administrator'));
}

//-----------------------------------------------------------------------------------------
//تابع بررسی و کسر سکه برای شروع بازی:
function checkAndDeductCoins($user_id, $required_coins = 2) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    // بررسی اشتراک فعال - اگر اشتراک دارد، سکه کسر نمی‌شود
    if (hasActiveSubscription($user_id)) {
        return true; // کاربر اشتراک دارد، نیازی به کسر سکه نیست
    }

    $userData = json_decode(file_get_contents($userFile), true);
    $currentCoins = isset($userData['userfild']['coins']) ? $userData['userfild']['coins'] : 20;

    if ($currentCoins < $required_coins) {
        return false; // سکه کافی نیست
    }

    // کسر سکه
    $userData['userfild']['coins'] = $currentCoins - $required_coins;
    $userData = json_encode($userData, true);
    file_put_contents($userFile, $userData);

    return true; // سکه با موفقیت کسر شد
}

//تابع بازگرداندن سکه (در صورت لغو بازی):
function refundCoins($user_id, $refund_coins = 2) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    $currentCoins = isset($userData['userfild']['coins']) ? $userData['userfild']['coins'] : 20;

    // بازگرداندن سکه
    $userData['userfild']['coins'] = $currentCoins + $refund_coins;
    $userData = json_encode($userData, true);
    file_put_contents($userFile, $userData);

    return true;
}

//تابع تنظیم زمان شروع بازی:
function setGameStartTime($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);
    $userData['userfild']['game_start_time'] = time();
    $userData = json_encode($userData, true);
    file_put_contents($userFile, $userData);

    return true;
}

//تابع بررسی زمان بازی (آیا 30 ثانیه گذشته؟):
function canEndGame($user_id, $required_seconds = 30) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return true; // اگر فایل وجود ندارد، اجازه پایان بازی
    }

    $userData = json_decode(file_get_contents($userFile), true);

    // اگر زمان شروع بازی ثبت نشده، اجازه پایان بازی
    if (!isset($userData['userfild']['game_start_time'])) {
        return true;
    }

    $gameStartTime = $userData['userfild']['game_start_time'];
    $currentTime = time();
    $elapsedTime = $currentTime - $gameStartTime;

    return $elapsedTime >= $required_seconds;
}

//تابع محاسبه زمان باقی‌مانده تا امکان پایان بازی:
function getRemainingTime($user_id, $required_seconds = 30) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return 0;
    }

    $userData = json_decode(file_get_contents($userFile), true);

    if (!isset($userData['userfild']['game_start_time'])) {
        return 0;
    }

    $gameStartTime = $userData['userfild']['game_start_time'];
    $currentTime = time();
    $elapsedTime = $currentTime - $gameStartTime;

    return max(0, $required_seconds - $elapsedTime);
}

//تابع بررسی اینکه آیا بازی بیشتر از 2 دقیقه طول کشیده است:
function isGameLongerThan2Minutes($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);

    if (!isset($userData['userfild']['game_start_time'])) {
        return false;
    }

    $gameStartTime = $userData['userfild']['game_start_time'];
    $currentTime = time();
    $elapsedTime = $currentTime - $gameStartTime;

    return $elapsedTime >= 120; // 2 دقیقه = 120 ثانیه
}

//تابع بررسی اشتراک فعال کاربر:
function hasActiveSubscription($user_id) {
    $userFile = "data/user/$user_id.json";
    if (!file_exists($userFile)) {
        return false;
    }

    $userData = json_decode(file_get_contents($userFile), true);

    // بررسی وجود اشتراک فعال
    if (isset($userData['userfild']['subscription']) &&
        isset($userData['userfild']['subscription']['is_active']) &&
        $userData['userfild']['subscription']['is_active'] &&
        isset($userData['userfild']['subscription']['end_date']) &&
        $userData['userfild']['subscription']['end_date'] > time()) {
        return true;
    }

    return false;
}

//تابع انتخاب سوال تصادفی حقیقت:
function getRandomTruthQuestion($type = 'normal', $gender = null) {
    $sampleData = json_decode(file_get_contents("sample.json"), true);

    if ($type == 'normal') {
        $questions = $sampleData['truth']['normal'];
    } else {
        // برای +18، اگر جنسیت مشخص نباشد، از هر دو جنسیت انتخاب می‌کنیم
        if ($gender && isset($sampleData['truth']['plus18'][$gender])) {
            $questions = $sampleData['truth']['plus18'][$gender];
        } else {
            // ترکیب سوالات هر دو جنسیت
            $boyQuestions = isset($sampleData['truth']['plus18']['boy']) ? $sampleData['truth']['plus18']['boy'] : [];
            $girlQuestions = isset($sampleData['truth']['plus18']['girl']) ? $sampleData['truth']['plus18']['girl'] : [];
            $questions = array_merge($boyQuestions, $girlQuestions);
        }
    }

    if (empty($questions)) {
        return "سوال حقیقت آماده‌ای موجود نیست.";
    }

    return $questions[array_rand($questions)];
}

//تابع انتخاب چالش تصادفی جرعت:
function getRandomDareChallenge($type = 'normal', $gender = null) {
    $sampleData = json_decode(file_get_contents("sample.json"), true);

    if ($type == 'normal') {
        $challenges = $sampleData['dare']['normal'];
    } else {
        // برای +18، اگر جنسیت مشخص نباشد، از هر دو جنسیت انتخاب می‌کنیم
        if ($gender && isset($sampleData['dare']['plus18'][$gender])) {
            $challenges = $sampleData['dare']['plus18'][$gender];
        } else {
            // ترکیب چالش‌های هر دو جنسیت
            $boyChallenges = isset($sampleData['dare']['plus18']['boy']) ? $sampleData['dare']['plus18']['boy'] : [];
            $girlChallenges = isset($sampleData['dare']['plus18']['girl']) ? $sampleData['dare']['plus18']['girl'] : [];
            $challenges = array_merge($boyChallenges, $girlChallenges);
        }
    }

    if (empty($challenges)) {
        return "چالش جرعت آماده‌ای موجود نیست.";
    }

    return $challenges[array_rand($challenges)];
}

//تابع اضافه کردن کاربر به صف جستجو:
function addToSearchQueue($user_id) {
    $queueFile = "data/search_queue.json";
    $queue = [];

    if (file_exists($queueFile)) {
        $queue = json_decode(file_get_contents($queueFile), true);
        if (!$queue) $queue = [];
    }

    // دریافت اطلاعات کاربر برای تعیین جنسیت و نوع بازی مطلوب
    $userFile = "data/user/$user_id.json";
    $userGender = "";
    $preferredGender = "random"; // پیش‌فرض رندوم

    if (file_exists($userFile)) {
        $userData = json_decode(file_get_contents($userFile), true);
        $userGender = isset($userData['userfild']['gender']) ? $userData['userfild']['gender'] : "";
        $preferredGender = isset($userData['userfild']['game_type']) ? $userData['userfild']['game_type'] : "random";
    }

    // حذف کاربر از صف در صورت وجود قبلی
    $queue = array_filter($queue, function($item) use ($user_id) {
        return $item['user_id'] != $user_id;
    });

    // اضافه کردن کاربر جدید به صف
    $queue[] = [
        'user_id' => $user_id,
        'timestamp' => time(),
        'game_type' => 'single',
        'user_gender' => $userGender,
        'preferred_gender' => $preferredGender
    ];

    file_put_contents($queueFile, json_encode($queue, JSON_PRETTY_PRINT));

    // تلاش برای پیدا کردن حریف
    findOpponent($user_id);
}

//تابع حذف کاربر از صف جستجو:
function removeFromSearchQueue($user_id) {
    $queueFile = "data/search_queue.json";
    if (!file_exists($queueFile)) {
        return;
    }

    $queue = json_decode(file_get_contents($queueFile), true);
    if (!$queue) return;

    $queue = array_filter($queue, function($item) use ($user_id) {
        return $item['user_id'] != $user_id;
    });

    file_put_contents($queueFile, json_encode(array_values($queue), JSON_PRETTY_PRINT));
}

//تابع پیدا کردن حریف:
function findOpponent($user_id) {
    $queueFile = "data/search_queue.json";
    if (!file_exists($queueFile)) {
        return false;
    }

    $queue = json_decode(file_get_contents($queueFile), true);
    if (!$queue || count($queue) < 2) {
        return false;
    }

    // پیدا کردن کاربر فعلی در صف
    $currentUserIndex = -1;
    $currentUser = null;
    foreach ($queue as $index => $item) {
        if ($item['user_id'] == $user_id) {
            $currentUserIndex = $index;
            $currentUser = $item;
            break;
        }
    }

    if ($currentUserIndex == -1 || !$currentUser) {
        return false;
    }

    // دریافت تنظیمات جنسیت مطلوب کاربر فعلی
    $currentUserPreferredGender = isset($currentUser['preferred_gender']) ? $currentUser['preferred_gender'] : 'random';
    $currentUserGender = isset($currentUser['user_gender']) ? $currentUser['user_gender'] : '';

    // پیدا کردن اولین کاربر مناسب (غیر از خودش)
    $opponent = null;
    $opponentIndex = -1;

    foreach ($queue as $index => $item) {
        if ($index != $currentUserIndex && $item['user_id'] != $user_id) {
            // بررسی اینکه کاربر هنوز آنلاین است
            $opponentFile = "data/user/{$item['user_id']}.json";
            if (file_exists($opponentFile)) {
                $opponentData = json_decode(file_get_contents($opponentFile), true);
                // بررسی اینکه کاربر در بازی نباشد
                if (!isset($opponentData['userfild']['ingame']) || $opponentData['userfild']['ingame'] != 'on') {

                    // بررسی تطبیق جنسیت
                    $opponentGender = isset($item['user_gender']) ? $item['user_gender'] : '';
                    $opponentPreferredGender = isset($item['preferred_gender']) ? $item['preferred_gender'] : 'random';

                    $isGenderMatch = false;

                    // اگر کاربر فعلی رندوم انتخاب کرده، با هر کسی بازی می‌کند
                    if ($currentUserPreferredGender == 'random') {
                        $isGenderMatch = true;
                    }
                    // اگر کاربر فعلی دختر می‌خواهد و حریف دختر است
                    elseif ($currentUserPreferredGender == 'girl' && $opponentGender == 'دختر') {
                        $isGenderMatch = true;
                    }
                    // اگر کاربر فعلی پسر می‌خواهد و حریف پسر است
                    elseif ($currentUserPreferredGender == 'boy' && $opponentGender == 'پسر') {
                        $isGenderMatch = true;
                    }

                    // همچنین باید بررسی کنیم که حریف هم با جنسیت کاربر فعلی موافق باشد
                    if ($isGenderMatch) {
                        $isOpponentMatch = false;

                        // اگر حریف رندوم انتخاب کرده، با هر کسی بازی می‌کند
                        if ($opponentPreferredGender == 'random') {
                            $isOpponentMatch = true;
                        }
                        // اگر حریف دختر می‌خواهد و کاربر فعلی دختر است
                        elseif ($opponentPreferredGender == 'girl' && $currentUserGender == 'دختر') {
                            $isOpponentMatch = true;
                        }
                        // اگر حریف پسر می‌خواهد و کاربر فعلی پسر است
                        elseif ($opponentPreferredGender == 'boy' && $currentUserGender == 'پسر') {
                            $isOpponentMatch = true;
                        }

                        if ($isOpponentMatch) {
                            $opponent = $item;
                            $opponentIndex = $index;
                            break;
                        }
                    }
                }
            }
        }
    }

    if ($opponent) {
        // حذف هر دو کاربر از صف
        unset($queue[$currentUserIndex]);
        unset($queue[$opponentIndex]);
        file_put_contents($queueFile, json_encode(array_values($queue), JSON_PRETTY_PRINT));

        // شروع بازی بین دو کاربر
        startRandomGame($user_id, $opponent['user_id']);
        return true;
    }

    return false;
}

//تابع شروع بازی تصادفی:
function startRandomGame($user1_id, $user2_id) {
    // کسر سکه از هر دو کاربر
    if (!checkAndDeductCoins($user1_id, 2)) {
        // بازگرداندن کاربران به صف در صورت خطا
        addToSearchQueue($user1_id);
        return false;
    }

    if (!checkAndDeductCoins($user2_id, 2)) {
        // بازگرداندن سکه کاربر اول و بازگرداندن به صف
        refundCoins($user1_id, 2);
        addToSearchQueue($user1_id);
        addToSearchQueue($user2_id);
        return false;
    }

    // دریافت اطلاعات کاربران
    $user1Data = json_decode(file_get_contents("data/user/$user1_id.json"), true);
    $user2Data = json_decode(file_get_contents("data/user/$user2_id.json"), true);

    $user1Nickname = !empty($user1Data['userfild']['nickname']) ? $user1Data['userfild']['nickname'] : "ناشناس";
    $user2Nickname = !empty($user2Data['userfild']['nickname']) ? $user2Data['userfild']['nickname'] : "ناشناس";

    $user1Token = isset($user1Data['userfild']['user_token']) ? $user1Data['userfild']['user_token'] : "نامشخص";
    $user2Token = isset($user2Data['userfild']['user_token']) ? $user2Data['userfild']['user_token'] : "نامشخص";

    // ویرایش پیام جستجو به "بازیکن یافت شد" برای هر دو کاربر
    if (isset($user1Data['userfild']['search_message_id'])) {
        jijibot('editmessagetext',[
            'chat_id' => $user1_id,
            'message_id' => $user1Data['userfild']['search_message_id'],
            'text' => "🔔 پایان جستجو

<blockquote>⚠️ هشدار سیستم : کاربر گرامی به هیچ وجه اطلاعات تماس خود را در اختیار افراد قرار ندهید.</blockquote>",
            'parse_mode' => 'HTML'
        ]);
        unset($user1Data['userfild']['search_message_id']);
    }

    if (isset($user2Data['userfild']['search_message_id'])) {
        jijibot('editmessagetext',[
            'chat_id' => $user2_id,
            'message_id' => $user2Data['userfild']['search_message_id'],
            'text' => "🔔 پایان جستجو

<blockquote>⚠️ هشدار سیستم : کاربر گرامی به هیچ وجه اطلاعات تماس خود را در اختیار افراد قرار ندهید.</blockquote>",
            'parse_mode' => 'HTML'
        ]);
        unset($user2Data['userfild']['search_message_id']);
    }

    // تعیین پیام بر اساس وضعیت اشتراک هر کاربر
    $user1CoinMessage = hasActiveSubscription($user1_id) ?
        "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
        "💰 2 سکه از حساب شما کسر شد.";

    $user2CoinMessage = hasActiveSubscription($user2_id) ?
        "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
        "💰 2 سکه از حساب شما کسر شد.";

    // ارسال پیام "بازیکن پیدا شد" به هر دو کاربر
    jijibot('sendmessage',[
        'chat_id'=>$user1_id,
        'text'=>"🎉 بازیکن پیدا شد!

🌟 بازی جدید با کاربر $user2Nickname (<a href=\"https://t.me/jdarebot?start=$user2Token\">$user2Token</a>)

$user1CoinMessage

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
        'parse_mode'=>'HTML',
        'disable_web_page_preview'=>true,
        'reply_markup'=>json_encode([
            'keyboard'=>[
                [
                    ['text'=>"👀 مشاهده حریف"],
                    ['text'=>"❌ پایان بازی"]
                ],
            ],
            'resize_keyboard'=>true
        ])
    ]);

    jijibot('sendmessage',[
        'chat_id'=>$user2_id,
        'text'=>"🎉 بازیکن پیدا شد!

🌟 بازی جدید با کاربر $user1Nickname (<a href=\"https://t.me/jdarebot?start=$user1Token\">$user1Token</a>)

$user2CoinMessage

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
        'parse_mode'=>'HTML',
        'disable_web_page_preview'=>true,
        'reply_markup'=>json_encode([
            'keyboard'=>[
                [
                    ['text'=>"👀 مشاهده حریف"],
                    ['text'=>"❌ پایان بازی"]
                ],
            ],
            'resize_keyboard'=>true
        ])
    ]);

    // قرعه‌کشی برای تعیین نوبت
    $array = array($user1_id, $user2_id);
    $random = array_rand($array);

    jijibot('sendmessage',[
        'chat_id'=>$array[$random],
        'text'=>"✨ نوبت شما است که سوال بپرسید

لطفا منتظر بمانید تا حریف شما جرعت یا حقیقت را انتخاب کند.",
    ]);

    $result = array_diff($array, array($array[$random]));
    $otherPlayer = reset($result);

    jijibot('sendmessage',[
        'chat_id'=>$otherPlayer,
        'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
                ],
            ]
        ])
    ]);

    // تنظیم وضعیت بازی برای هر دو کاربر
    $user1Data["userfild"]["rival"] = "$user2_id";
    $user1Data["userfild"]["ingame"] = "on";
    $user1Data = json_encode($user1Data, true);
    file_put_contents("data/user/$user1_id.json", $user1Data);

    $user2Data["userfild"]["rival"] = "$user1_id";
    $user2Data["userfild"]["ingame"] = "on";
    $user2Data = json_encode($user2Data, true);
    file_put_contents("data/user/$user2_id.json", $user2Data);

    // تنظیم زمان شروع بازی برای هر دو کاربر
    setGameStartTime($user1_id);
    setGameStartTime($user2_id);

    return true;
}

//-----------------------------------------------------------------------------------------
//تابع بررسی مسدودیت کاربر:
function is_user_blocked($sender_id, $target_id) {
    $targetUserFile = "data/user/$target_id.json";
    if(file_exists($targetUserFile)) {
        $targetUserData = json_decode(file_get_contents($targetUserFile), true);
        $blockedUsers = isset($targetUserData['userfild']['blocked_users']) ? $targetUserData['userfild']['blocked_users'] : [];
        return in_array($sender_id, $blockedUsers);
    }
    return false;
}

//-----------------------------------------------------------------------------------------
//توابع مربوط به درگاه پرداخت:

// تابع دریافت قیمت پلن (بر اساس قیمت‌های bot.php)
function getPlanPrice($plan) {
    $prices = array(
        'sub_30' => 21000,    // 21 هزار تومان
        'sub_60' => 42000,    // 42 هزار تومان
        'sub_90' => 63000,    // 63 هزار تومان
        'sub_365' => 252000   // 252 هزار تومان
    );
    return isset($prices[$plan]) ? $prices[$plan] : 0;
}

// تابع تبدیل تومان به ریال
function convertToRials($tomans) {
    return $tomans * 10;
}

// تابع دریافت نام پلن
function getPlanName($plan) {
    $names = array(
        'sub_30' => 'اشتراک 30 روزه',
        'sub_60' => 'اشتراک 60 روزه',
        'sub_90' => 'اشتراک 90 روزه',
        'sub_365' => 'اشتراک 1 ساله'
    );
    return isset($names[$plan]) ? $names[$plan] : 'نامشخص';
}

// تابع دریافت تعداد روزهای پلن
function getPlanDays($plan) {
    $days = array(
        'sub_30' => 30,
        'sub_60' => 60,
        'sub_90' => 90,
        'sub_365' => 365
    );
    return isset($days[$plan]) ? $days[$plan] : 0;
}

// تابع ایجاد لینک پرداخت با درگاه Zibal
function generatePaymentLink($user_id, $amount, $plan) {
    // کلید مرچنت Zibal
    $merchantKey = "zibal";

    // ایجاد شناسه سفارش منحصر به فرد - format: SPX-TIMESTAMP-USERID
    $orderId = "SPX-" . time() . "-" . $user_id;

    // آدرس بازگشت از درگاه پرداخت (callback URL)
    $callbackUrl = "https://speedx-team.ir/jDare-Bot/callback.php";

    // توضیحات پرداخت
    $description = "خرید " . getPlanName($plan) . " برای کاربر " . $user_id;

    // اطلاعات پرداخت
    $parameters = array(
        "merchant" => $merchantKey,
        "callbackUrl" => $callbackUrl,
        "amount" => $amount,
        "orderId" => $orderId,
        "description" => $description
    );

    // ذخیره اطلاعات سفارش در سشن
    Session::set($user_id, 'payment_plan', $plan);
    Session::set($user_id, 'payment_order_id', $orderId);

    // ایجاد پوشه logs در صورت عدم وجود
    if (!file_exists("data/logs")) {
        mkdir("data/logs", 0777, true);
    }

    // لاگ کردن اطلاعات درخواست پرداخت
    file_put_contents('data/logs/payment_request_log.txt', date('Y-m-d H:i:s') . " Payment request: user_id=$user_id, plan=$plan, amount=$amount, orderId=$orderId\n", FILE_APPEND);

    // ارسال درخواست به Zibal و دریافت پاسخ
    $result = postToZibal('request', $parameters);

    // لاگ کردن پاسخ درخواست
    file_put_contents('data/logs/payment_request_log.txt', date('Y-m-d H:i:s') . " Response: " . json_encode($result) . "\n", FILE_APPEND);

    // بررسی پاسخ
    if ($result === false) {
        // خطا در ارتباط با درگاه
        file_put_contents('data/logs/payment_request_log.txt', date('Y-m-d H:i:s') . " ERROR: Failed to connect to Zibal gateway\n", FILE_APPEND);
        return "https://speedx-team.ir/jDare-Bot/payment-error.php?error=connection_failed";
    }

    if (!isset($result->result)) {
        // پاسخ نامعتبر از درگاه
        file_put_contents('data/logs/payment_request_log.txt', date('Y-m-d H:i:s') . " ERROR: Invalid response from Zibal\n", FILE_APPEND);
        return "https://speedx-team.ir/jDare-Bot/payment-error.php?error=invalid_response";
    }

    // اگر درخواست با موفقیت انجام شد، لینک پرداخت را برگرداند
    if ($result->result == 100) {
        if (isset($result->trackId)) {
            return "https://gateway.zibal.ir/start/" . $result->trackId;
        } else {
            file_put_contents('data/logs/payment_request_log.txt', date('Y-m-d H:i:s') . " ERROR: trackId not found in response\n", FILE_APPEND);
            return "https://speedx-team.ir/jDare-Bot/payment-error.php?error=no_track_id";
        }
    }

    // در صورت خطا، یک لینک خطا برگرداند
    $errorCode = $result->result ?? 'unknown';
    $errorMessage = isset($result->message) ? $result->message : 'Unknown error';

    file_put_contents('data/logs/payment_request_log.txt', date('Y-m-d H:i:s') . " ERROR: Zibal error code $errorCode: $errorMessage\n", FILE_APPEND);

    return "https://speedx-team.ir/jDare-Bot/payment-error.php?error=zibal_error&code=$errorCode&message=" . urlencode($errorMessage);
}

// تابع ارسال درخواست به API درگاه Zibal
function postToZibal($path, $parameters) {
    $url = 'https://gateway.zibal.ir/v1/' . $path;

    // ایجاد پوشه logs در صورت عدم وجود
    if (!file_exists("data/logs")) {
        mkdir("data/logs", 0777, true);
    }

    // لاگ درخواست
    file_put_contents('data/logs/zibal_debug.txt', date('Y-m-d H:i:s') . " REQUEST to $url: " . json_encode($parameters) . "\n", FILE_APPEND);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($parameters));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // لاگ پاسخ
    file_put_contents('data/logs/zibal_debug.txt', date('Y-m-d H:i:s') . " RESPONSE (HTTP $httpCode): $result\n", FILE_APPEND);

    if ($curlError) {
        file_put_contents('data/logs/zibal_debug.txt', date('Y-m-d H:i:s') . " CURL ERROR: $curlError\n", FILE_APPEND);
        return false;
    }

    if ($httpCode !== 200) {
        file_put_contents('data/logs/zibal_debug.txt', date('Y-m-d H:i:s') . " HTTP ERROR: Code $httpCode\n", FILE_APPEND);
        return false;
    }

    return json_decode($result);
}

//-----------------------------------------------------------------------------------------
//تابع نمایش پیام جوین اجباری در editmessagetext :
function force_join_edit($chatid, $messageid, $firstname, $usernamebot) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"سلام $firstname 👋

برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔔 عضویت در کانال دوم",'url'=>"https://t.me/speedxteam"],
                    ['text'=>"🔔 عضویت در کانال اول",'url'=>"https://t.me/speedx_bots"]
                ],
                [
                    ['text'=>"🔄 بررسی مجدد عضویت",'callback_data'=>"check_membership"]
                ]
            ]
        ])
    ]);
}

//-----------------------------------------------------------------------------------------
// تابع تبدیل تاریخ میلادی به شمسی
function gregorian_to_jalali($gy, $gm, $gd) {
    $g_d_m = array(0, 31, 59, 90, 120, 151, 181, 212, 243, 273, 304, 334);
    if ($gy <= 1600) {
        $jy = 0;
        $gy -= 621;
    } else {
        $jy = 979;
        $gy -= 1600;
    }
    if ($gm > 2) {
        $gy2 = $gy + 1;
    } else {
        $gy2 = $gy;
    }
    $days = (365 * $gy) + ((int)($gy2 / 4)) - ((int)($gy2 / 100)) + ((int)($gy2 / 400)) - 80 + $gd + $g_d_m[$gm - 1];
    $jy += 33 * ((int)($days / 12053));
    $days %= 12053;
    $jy += 4 * ((int)($days / 1461));
    $days %= 1461;
    if ($days > 365) {
        $jy += (int)(($days - 1) / 365);
        $days = ($days - 1) % 365;
    }
    if ($days < 186) {
        $jp = 1 + (int)($days / 31);
        $jd = 1 + ($days % 31);
    } else {
        $jp = 7 + (int)(($days - 186) / 30);
        $jd = 1 + (($days - 186) % 30);
    }
    return array($jy, $jp, $jd);
}

// تابع دریافت تاریخ شمسی فعلی
function get_current_jalali_date() {
    $current_date = getdate();
    // اضافه کردن یک روز برای تصحیح
    $timestamp = mktime(0, 0, 0, $current_date['mon'], $current_date['mday'] + 1, $current_date['year']);
    $corrected_date = getdate($timestamp);
    $jalali = gregorian_to_jalali($corrected_date['year'], $corrected_date['mon'], $corrected_date['mday']);
    return $jalali[0] . '/' . sprintf('%02d', $jalali[1]) . '/' . sprintf('%02d', $jalali[2]);
}

//-----------------------------------------------------------------------------------------
//متغیر ها :
// msg
$Dev = array("613771182","000000000","000000000","000000000"); // put id of admins
$usernamebot = "jDareBot";
$channel1 = "speedx_bots";
$channel2 = "speedxteam";
$botid = "000000000";
//-----------------------------------------------------------------------------------------------
$update = json_decode(file_get_contents('php://input'));
$message = $update->message;
$from_id = $message->from->id;
$chat_id = $message->chat->id;
$message_id = $message->message_id;
$first_name = $message->from->first_name;
$username = $message->from->username;
$textmassage = $message->text;
$messageid = $update->callback_query->message->message_id;
$tc = $update->message->chat->type;
$chatid = $update->callback_query->message->chat->id;
$fromid = $update->callback_query->from->id;
$data = $update->callback_query->data;
$membercall = $update->callback_query->id;
$firstname = $update->callback_query->from->first_name;
$usernameca = $update->callback_query->from->username;
//=================================================================================================
@$juser = json_decode(file_get_contents("data/user/$from_id.json"),true);
@$cuser = json_decode(file_get_contents("data/user/$fromid.json"),true);
@$getgp = json_decode(file_get_contents("data/gp/$chat_id.json"),true);
@$getgpc = json_decode(file_get_contents("data/gp/$chatid.json"),true);
@$database = json_decode(file_get_contents("data/database.json"),true);
@$rival = json_decode(file_get_contents("data/rival.json"),true);

// بررسی و اضافه کردن فیلد join_date برای کاربران قدیمی
if($juser && !isset($juser["userfild"]["join_date"])) {
    $juser["userfild"]["join_date"] = "نامشخص";
    $juser = json_encode($juser, true);
    file_put_contents("data/user/$from_id.json", $juser);
    $juser = json_decode($juser, true);
}

if($cuser && !isset($cuser["userfild"]["join_date"])) {
    $cuser["userfild"]["join_date"] = "نامشخص";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    $cuser = json_decode($cuser, true);
}

// بررسی و اضافه کردن فیلد phone برای کاربران قدیمی
if($juser && !isset($juser["userfild"]["phone"])) {
    $juser["userfild"]["phone"] = "";
    $juser = json_encode($juser, true);
    file_put_contents("data/user/$from_id.json", $juser);
    $juser = json_decode($juser, true);
}

if($cuser && !isset($cuser["userfild"]["phone"])) {
    $cuser["userfild"]["phone"] = "";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    $cuser = json_decode($cuser, true);
}

// بررسی و اضافه کردن فیلد photo برای کاربران قدیمی
if($juser && !isset($juser["userfild"]["photo"])) {
    $juser["userfild"]["photo"] = "";
    $juser = json_encode($juser, true);
    file_put_contents("data/user/$from_id.json", $juser);
    $juser = json_decode($juser, true);
}

if($cuser && !isset($cuser["userfild"]["photo"])) {
    $cuser["userfild"]["photo"] = "";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    $cuser = json_decode($cuser, true);
}

// بررسی و اضافه کردن فیلد user_token برای کاربران قدیمی
if($juser && !isset($juser["userfild"]["user_token"])) {
    $juser["userfild"]["user_token"] = generateUniqueUserToken();
    $juser = json_encode($juser, true);
    file_put_contents("data/user/$from_id.json", $juser);
    $juser = json_decode($juser, true);
}

if($cuser && !isset($cuser["userfild"]["user_token"])) {
    $cuser["userfild"]["user_token"] = generateUniqueUserToken();
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    $cuser = json_decode($cuser, true);
}

// بررسی و اضافه کردن فیلد last_visit_timestamp برای کاربران قدیمی
if($juser && !isset($juser["userfild"]["last_visit_timestamp"])) {
    $juser["userfild"]["last_visit_timestamp"] = time();
    $juser = json_encode($juser, true);
    file_put_contents("data/user/$from_id.json", $juser);
    $juser = json_decode($juser, true);
}

if($cuser && !isset($cuser["userfild"]["last_visit_timestamp"])) {
    $cuser["userfild"]["last_visit_timestamp"] = time();
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    $cuser = json_decode($cuser, true);
}
//==================================================================
if($textmassage=="/start"){
if(check_membership($from_id, $channel1, $channel2)){
jijibot('sendmessage',[
	'chat_id'=>$chat_id,
	'text'=>"سلام $first_name 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
	['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
	],
	[
	['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
	['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
	],
			[
	['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
	],
			[
	['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
	],
		[
	['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
	],
		[
	['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
	],
              ]
        ])
    		]);
}
else
{
force_join($chat_id, $first_name, $usernamebot);
}

// ایجاد کاربر جدید در صورت عدم وجود
if(!file_exists("data/user/$from_id.json")){
// ایجاد ساختار اولیه کاربر جدید
$new_user = array(
    "userfild" => array(
        "step" => "none",
        "ingame" => "off",
        "rival" => "",
        "nickname" => "",
        "gender" => "",
        "city" => "",
        "age" => "",
        "privacy" => "غیرفعال",
        "join_date" => get_current_jalali_date(),
        "phone" => "",
        "photo" => "",
        "coins" => 20,
        "score" => 5,
        "user_token" => generateUniqueUserToken(),
        "last_visit" => get_current_jalali_date(),
        "last_visit_timestamp" => time()
    )
);
$new_user = json_encode($new_user, true);
file_put_contents("data/user/$from_id.json", $new_user);
}
}
elseif(strpos($textmassage , '/start ') !== false  ) {
$start = str_replace("/start ","",$textmassage);


// بررسی اینکه آیا پارامتر برای بازی دوستانه است
if(strpos($start, 'p') === 0 && strlen($start) == 8) {
    // استخراج کد بازی از p+7کاراکتر
    $gameCode = substr($start, 1); // حذف p از ابتدا
    $targetUserId = findUserByGameCode($gameCode);

    if($targetUserId) {
        $userInfo = ['user_id' => $targetUserId];
    } else {
        $userInfo = null;
    }

    if($userInfo) {
        $targetUserId = $userInfo['user_id'];

        // بررسی اینکه کاربر با خودش بازی نکند
        if($targetUserId == $from_id) {
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"❌ شما نمی‌توانید با خودتان بازی کنید!",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                        ],
                    ]
                ])
            ]);
        } else {
            // بررسی عضویت در کانال‌ها
            if(!check_membership($from_id, $channel1, $channel2)){
                force_join($chat_id, $first_name, $usernamebot);
                return;
            }

            // بررسی وجود کاربر در دیتابیس و ایجاد اکانت در صورت عدم وجود
            if(!file_exists("data/user/$from_id.json")){
                // ایجاد ساختار اولیه کاربر جدید
                $new_user = array(
                    "userfild" => array(
                        "step" => "none",
                        "ingame" => "off",
                        "rival" => "",
                        "nickname" => "",
                        "gender" => "",
                        "city" => "",
                        "age" => "",
                        "privacy" => "غیرفعال",
                        "join_date" => get_current_jalali_date(),
                        "phone" => "",
                        "photo" => "",
                        "coins" => 20,
                        "score" => 5,
                        "user_token" => generateUniqueUserToken(),
                        "last_visit" => get_current_jalali_date(),
                        "last_visit_timestamp" => time()
                    )
                );
                $new_user = json_encode($new_user, true);
                file_put_contents("data/user/$from_id.json", $new_user);
                // بازخوانی اطلاعات کاربر
                $juser = json_decode(file_get_contents("data/user/$from_id.json"), true);
            }

            // بررسی وجود نام مستعار
            if(empty($juser["userfild"]["nickname"])) {
                jijibot('sendmessage',[
                    'chat_id'=>$chat_id,
                    'text'=>"📋 قوانین و مقررات ربات

⚠️ برای شروع بازی ابتدا باید قوانین زیر را مطالعه و تایید کنید:

🔸 از کلمات رکیک و توهین آمیز استفاده نکنید
🔸 محتوای غیراخلاقی و نامناسب ارسال نکنید
🔸 از اسپم و ارسال پیام‌های تکراری خودداری کنید
🔸 به سایر کاربران احترام بگذارید
🔸 از ربات برای اهداف غیرقانونی استفاده نکنید
🔸 اطلاعات شخصی خود و دیگران را فاش نکنید

⚖️ هرگونه فعالیت انجام شده اعم از متن و یا سایر محتوا های ارسالی کاملا به شخص مربوط شده و ربات در این مورد صلب مسیولیت می کند.

⚡️ نقض قوانین منجر به مسدود شدن حساب کاربری شما خواهد شد.",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"✅ قوانین را تایید می‌کنم",'callback_data'=>"accept_rules_for_game"]
                            ],
                            [
                                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                            ],
                        ]
                    ])
                ]);
                // تنظیم step برای تایید قوانین
                $juser["userfild"]["step"] = "accept_rules_for_game";
                $juser["userfild"]["game_target"] = $targetUserId; // ذخیره شناسه کاربر هدف
                $juser = json_encode($juser, true);
                file_put_contents("data/user/$from_id.json", $juser);
                return;
            }

            // بررسی موجودی سکه کاربر (فقط برای کاربران بدون اشتراک)
            $currentUserHasSubscription = hasActiveSubscription($from_id);
            if (!$currentUserHasSubscription) {
                $userCoins = isset($juser['userfild']['coins']) ? $juser['userfild']['coins'] : 20;
                if ($userCoins < 2) {
                    jijibot('sendmessage',[
                        'chat_id'=>$chat_id,
                        'text'=>"❌ سکه کافی ندارید!

برای شروع بازی نیاز به 2 سکه دارید.
موجودی فعلی شما: $userCoins سکه

می‌توانید از طریق دکمه‌های زیر سکه دریافت کنید:",
                        'reply_markup'=>json_encode([
                            'inline_keyboard'=>[
                                [
                                    ['text'=>"🎁 کد هدیه",'callback_data'=>"gift_code"],
                                    ['text'=>"🪙 سکه روزانه",'callback_data'=>"daily_coin"]
                                ],
                                [
                                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                                ],
                            ]
                        ])
                    ]);
                    return;
                }
            }

            // شروع بازی دوستانه
            if($juser["userfild"]["ingame"] == "on"){
                jijibot('sendmessage',[
                    'chat_id'=>$chat_id,
                    'text'=>"🌟 شما یک بازی در حال انجام دارید ابتدا ان را پایان دهید",
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👀 مشاهده حریف"],
                                ['text'=>"❌ پایان بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
            }
            else
            {
                // بررسی موجودی سکه کاربر هدف (فقط اگر هیچ کدام اشتراک ندارند)
                $targetUserHasSubscription = hasActiveSubscription($targetUserId);

                // اگر هیچ کدام اشتراک ندارند، هر دو باید سکه کافی داشته باشند
                if (!$currentUserHasSubscription && !$targetUserHasSubscription) {
                    $targetUserData = json_decode(file_get_contents("data/user/$targetUserId.json"), true);
                    $targetCoins = isset($targetUserData['userfild']['coins']) ? $targetUserData['userfild']['coins'] : 20;

                    if ($targetCoins < 2) {
                        jijibot('sendmessage',[
                            'chat_id'=>$chat_id,
                            'text'=>"❌ کاربر مورد نظر سکه کافی ندارد!

برای شروع بازی، کاربران بدون اشتراک باید حداقل 2 سکه داشته باشند.",
                            'reply_markup'=>json_encode([
                                'inline_keyboard'=>[
                                    [
                                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                                    ],
                                ]
                            ])
                        ]);
                        return;
                    }
                }
                // اگر فقط کاربر هدف اشتراک ندارد، باید سکه کافی داشته باشد
                else if (!$targetUserHasSubscription) {
                    $targetUserData = json_decode(file_get_contents("data/user/$targetUserId.json"), true);
                    $targetCoins = isset($targetUserData['userfild']['coins']) ? $targetUserData['userfild']['coins'] : 20;

                    if ($targetCoins < 2) {
                        jijibot('sendmessage',[
                            'chat_id'=>$chat_id,
                            'text'=>"❌ کاربر مورد نظر سکه کافی ندارد!

کاربر مورد نظر اشتراک ندارد و باید حداقل 2 سکه داشته باشد.",
                            'reply_markup'=>json_encode([
                                'inline_keyboard'=>[
                                    [
                                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                                    ],
                                ]
                            ])
                        ]);
                        return;
                    }
                }

                // کسر سکه از هر دو کاربر
                if (!checkAndDeductCoins($from_id, 2)) {
                    jijibot('sendmessage',[
                        'chat_id'=>$chat_id,
                        'text'=>"❌ خطا در کسر سکه! لطفا دوباره تلاش کنید.",
                    ]);
                    return;
                }

                if (!checkAndDeductCoins($targetUserId, 2)) {
                    // بازگرداندن سکه کاربر اول
                    refundCoins($from_id, 2);
                    jijibot('sendmessage',[
                        'chat_id'=>$chat_id,
                        'text'=>"❌ خطا در کسر سکه حریف! بازی لغو شد.",
                    ]);
                    return;
                }

                // دریافت اطلاعات کاربر هدف
                $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";
                $targetToken = isset($targetUserData['userfild']['user_token']) ? $targetUserData['userfild']['user_token'] : "نامشخص";

                // تعیین پیام بر اساس وضعیت اشتراک
                $coinMessage = $currentUserHasSubscription ?
                    "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
                    "💰 2 سکه از حساب شما کسر شد.";

                jijibot('sendmessage',[
                    'chat_id'=>$chat_id,
                    'text'=>"🌟 بازی جدید با کاربر $targetNickname (<a href=\"https://t.me/jdarebot?start=$targetToken\">$targetToken</a>)

$coinMessage

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
                    'parse_mode'=>'HTML',
                    'disable_web_page_preview'=>true,
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👀 مشاهده حریف"],
                                ['text'=>"❌ پایان بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
                // دریافت اطلاعات کاربر فعلی برای نمایش به طرف مقابل
                $currentUserData = json_decode(file_get_contents("data/user/$from_id.json"), true);
                $currentNickname = !empty($currentUserData['userfild']['nickname']) ? $currentUserData['userfild']['nickname'] : "ناشناس";
                $currentToken = isset($currentUserData['userfild']['user_token']) ? $currentUserData['userfild']['user_token'] : "نامشخص";

                // تعیین پیام بر اساس وضعیت اشتراک کاربر هدف
                $targetCoinMessage = $targetUserHasSubscription ?
                    "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
                    "💰 2 سکه از حساب شما کسر شد.";

                jijibot('sendmessage',[
                    'chat_id'=>$targetUserId,
                    'text'=>"🌟 بازی جدید با کاربر $currentNickname (<a href=\"https://t.me/jdarebot?start=$currentToken\">$currentToken</a>)

$targetCoinMessage

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
                    'parse_mode'=>'HTML',
                    'disable_web_page_preview'=>true,
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👀 مشاهده حریف"],
                                ['text'=>"❌ پایان بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
                $array = array("$from_id",$targetUserId);
                $random = array_rand($array);
                jijibot('sendmessage',[
                    'chat_id'=>$array[$random],
                    'text'=>"✨ نوبت شما است که سوال بپرسید

لطفا منتظر بمانید تا حریف شما جرعت یا حقیقت را انتخاب کند.",
                ]);
                $result = array_diff($array , array($array[$random]));
                jijibot('sendmessage',[
                    'chat_id'=>$result[0],
                    'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
                            ],
                        ]
                    ])
                ]);
                jijibot('sendmessage',[
                    'chat_id'=>$result[1],
                    'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
                            ],
                        ]
                    ])
                ]);
                $juser["userfild"]["rival"]="$targetUserId";
                $juser["userfild"]["ingame"]="on";
                $juser = json_encode($juser,true);
                file_put_contents("data/user/$from_id.json",$juser);
                $userrival = $targetUserId;
                $getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
                $getrival["userfild"]["rival"]="$from_id";
                $getrival["userfild"]["ingame"]="on";
                $getrival = json_encode($getrival,true);
                file_put_contents("data/user/$userrival.json",$getrival);

                // تنظیم زمان شروع بازی برای هر دو کاربر
                setGameStartTime($from_id);
                setGameStartTime($userrival);
            }
        }
    } else {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ لینک دعوت نامعتبر!

این لینک دعوت معتبر نیست یا منقضی شده است.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    }
}
// بررسی اینکه آیا پارامتر یک ID عددی است برای سیستم زیرمجموعه گیری
elseif(is_numeric($start)) {
    $referrerId = intval($start);

    // بررسی اینکه کاربر با خودش زیرمجموعه نشود
    if($referrerId == $from_id) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ شما نمی‌توانید از لینک خودتان استفاده کنید!",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    } else {
        // بررسی وجود کاربر دعوت کننده
        if(file_exists("data/user/$referrerId.json")) {
            // بررسی عضویت در کانال‌ها
            if(!check_membership($from_id, $channel1, $channel2)){
                force_join($chat_id, $first_name, $usernamebot);
                return;
            }

            // بررسی اینکه آیا کاربر جدید است
            if(!file_exists("data/user/$from_id.json")){
                // ایجاد ساختار اولیه کاربر جدید
                $new_user = array(
                    "userfild" => array(
                        "step" => "none",
                        "ingame" => "off",
                        "rival" => "",
                        "nickname" => "",
                        "gender" => "",
                        "city" => "",
                        "age" => "",
                        "privacy" => "غیرفعال",
                        "join_date" => get_current_jalali_date(),
                        "phone" => "",
                        "photo" => "",
                        "coins" => 20,
                        "score" => 5,
                        "user_token" => generateUniqueUserToken(),
                        "last_visit" => get_current_jalali_date(),
                        "last_visit_timestamp" => time(),
                        "referred_by" => $referrerId // ذخیره کاربر دعوت کننده
                    )
                );
                $new_user = json_encode($new_user, true);
                file_put_contents("data/user/$from_id.json", $new_user);

                // اعطای 10 سکه به کاربر دعوت کننده
                $referrerData = json_decode(file_get_contents("data/user/$referrerId.json"), true);
                $referrerCoins = isset($referrerData['userfild']['coins']) ? $referrerData['userfild']['coins'] : 20;
                $referrerNickname = !empty($referrerData['userfild']['nickname']) ? $referrerData['userfild']['nickname'] : "ناشناس";
                $referrerData['userfild']['coins'] = $referrerCoins + 10;
                $referrerData = json_encode($referrerData, true);
                file_put_contents("data/user/$referrerId.json", $referrerData);
                jijibot('sendmessage',[
                    'chat_id'=>$referrerId,
                    'text'=>"🎉 تبریک! کاربر جدیدی از طریق لینک شما وارد ربات شد!

👤 کاربر: $first_name
🪙 پاداش: 10 سکه به حساب شما اضافه شد
💰 موجودی جدید: " . ($referrerCoins + 10) . " سکه

🔗 برای دعوت کاربران بیشتر از بخش 'زیرمجموعه گیری' استفاده کنید.",
                ]);

                // ارسال پیام خوش آمدگویی به کاربر جدید
                jijibot('sendmessage',[
                    'chat_id'=>$chat_id,
                    'text'=>"🎉 سلام $first_name! خوش آمدید!

شما از طریق لینک دعوت کاربر $referrerNickname وارد ربات شدید.

🎁 به عنوان هدیه خوش آمدگویی، 20 سکه به حساب شما اضافه شد!

به ربات چالشی جرعت و حقیقت خوش آمدید!

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
                    'parse_mode'=>'HTML',
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
                                ['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
                            ],
                            [
                                ['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
                                ['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
                            ],
                            [
                                ['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
                            ],
                            [
                                ['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
                            ],
                            [
                                ['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
                            ],
                            [
                                ['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
                            ],
                        ]
                    ])
                ]);
            } else {
                // کاربر قبلاً وجود دارد
                jijibot('sendmessage',[
                    'chat_id'=>$chat_id,
                    'text'=>"سلام $first_name 👋

شما قبلاً عضو ربات هستید!

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
                    'parse_mode'=>'HTML',
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
                                ['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
                            ],
                            [
                                ['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
                                ['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
                            ],
                            [
                                ['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
                            ],
                            [
                                ['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
                            ],
                            [
                                ['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
                            ],
                            [
                                ['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
                            ],
                        ]
                    ])
                ]);
            }
        } else {
            // کاربر دعوت کننده وجود ندارد
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"❌ لینک دعوت نامعتبر!

این لینک دعوت معتبر نیست یا منقضی شده است.",
            ]);
        }
    }
}
// بررسی اینکه آیا پارامتر یک توکن است برای نمایش پروفایل
elseif(strlen($start) >= 5 && strlen($start) <= 10 && ctype_alnum($start)) {
    // این یک توکن است - نمایش پروفایل
    $userInfo = findUserByToken($start);

    if($userInfo) {
        $targetUser = $userInfo['data'];
        $targetUserId = $userInfo['user_id'];

        // بررسی حریم خصوصی کاربر هدف
        $privacy = isset($targetUser['userfild']['privacy']) ? $targetUser['userfild']['privacy'] : 'غیرفعال';

        if($privacy == 'فعال' && $targetUserId != $from_id) {
            // کاربر حریم خصوصی فعال دارد
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"🔒 این کاربر حریم خصوصی فعال دارد و پروفایل او قابل مشاهده نیست.",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                        ],
                    ]
                ])
            ]);
        } else {
            // نمایش پروفایل کاربر
            // بررسی عضویت
            if(check_membership($from_id, $channel1, $channel2)){
                // دریافت اطلاعات کاربر
                $baseNickname = !empty($targetUser['userfild']['nickname']) ? $targetUser['userfild']['nickname'] : "ناشناس";
                $gender = !empty($targetUser['userfild']['gender']) ? $targetUser['userfild']['gender'] : "تنظیم نشده";
                $city = !empty($targetUser['userfild']['city']) ? $targetUser['userfild']['city'] : "تنظیم نشده";
                $age = !empty($targetUser['userfild']['age']) ? $targetUser['userfild']['age'] : "تنظیم نشده";
                $join_date = isset($targetUser['userfild']['join_date']) ? $targetUser['userfild']['join_date'] : "نامشخص";
                $score = isset($targetUser['userfild']['score']) ? $targetUser['userfild']['score'] : 0;
                $coins = isset($targetUser['userfild']['coins']) ? $targetUser['userfild']['coins'] : 0;

                // بررسی وضعیت VIP
                $isVIP = false;
                if (isset($targetUser['userfild']['subscription']['is_active']) &&
                    $targetUser['userfild']['subscription']['is_active'] === true &&
                    isset($targetUser['userfild']['subscription']['end_date']) &&
                    $targetUser['userfild']['subscription']['end_date'] > time()) {
                    $isVIP = true;
                }

                // اضافه کردن ایموجی ✨ برای کاربران VIP
                $nickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;
                $vipStatus = $isVIP ? "\n✱ این کاربر VIP است" : "";

                // دریافت توکن کاربر و آخرین بازدید
                $userToken = isset($targetUser['userfild']['user_token']) ? $targetUser['userfild']['user_token'] : "نامشخص";
                $lastVisit = getLastVisitStatus($targetUser);

                // متن کپشن پروفایل
                $profileCaption = "👤 پروفایل کاربر

✱ نام مستعار : $nickname$vipStatus
✱ توکن کاربر : <a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>
✱ آخرین بازدید : $lastVisit
✱ تاریخ ورود : $join_date
✱ جنسیت : $gender
✱ شهر : $city
✱ سن : $age
✱ امتیاز : $score
✱ سکه : $coins";

                // بررسی تصویر ذخیره شده کاربر
                $userPhoto = isset($targetUser['userfild']['photo']) ? $targetUser['userfild']['photo'] : "";

                if (!empty($userPhoto)) {
                    // کاربر تصویر ذخیره شده دارد - ارسال تصویر ذخیره شده
                    jijibot('sendPhoto', [
                        'chat_id' => $chat_id,
                        'photo' => $userPhoto,
                        'caption' => $profileCaption,
                        'parse_mode' => 'HTML',
                        'reply_markup' => json_encode([
                            'inline_keyboard' => [
                                [
                                    ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                                    ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                                ],
                                [
                                    ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                                    ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                                ],
                                [
                                    ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                                    ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                                ],
                                [
                                    ['text' => "❌ بستن", 'callback_data' => "close"]
                                ]
                            ]
                        ])
                    ]);
                } else {
                    // کاربر تصویر ذخیره شده ندارد - استفاده از تصویر پیش‌فرض
                    $defaultPhotoUrl = "https://t.me/fjw2w9c9/1855";

                    jijibot('sendPhoto', [
                        'chat_id' => $chat_id,
                        'photo' => $defaultPhotoUrl,
                        'caption' => $profileCaption,
                        'parse_mode' => 'HTML',
                        'reply_markup' => json_encode([
                            'inline_keyboard' => [
                                [
                                    ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                                    ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                                ],
                                [
                                    ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                                    ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                                ],
                                [
                                    ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                                    ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                                ],
                                [
                                    ['text' => "❌ بستن", 'callback_data' => "close"]
                                ]
                            ]
                        ])
                    ]);
                }
            } else {
                force_join($chat_id, $first_name, $usernamebot);
            }
        }
    } else {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ کاربر با این توکن یافت نشد!",
        ]);
    }
}
else
{
jijibot('sendmessage',[
	'chat_id'=>$chat_id,
	'text'=>"سلام $first_name 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
	['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
	],
	[
	['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
	['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
	],
			[
	['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
	],
			[
	['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
	],
		[
	['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
	],
		[
	['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
	],
              ]
        ])
    		]);	
}
}
elseif(strpos($textmassage, '/u') === 0 && strlen($textmassage) >= 7 && strlen($textmassage) <= 12) {
// استخراج توکن از دستور /u{token}
$token = substr($textmassage, 2); // حذف /u از ابتدا

if(strlen($token) >= 5 && strlen($token) <= 10 && ctype_alnum($token)) {
    // جستجوی کاربر با توکن
    $userInfo = findUserByToken($token);

    if($userInfo) {
        $targetUser = $userInfo['data'];
        $targetUserId = $userInfo['user_id'];

        // بررسی حریم خصوصی کاربر هدف
        $privacy = isset($targetUser['userfild']['privacy']) ? $targetUser['userfild']['privacy'] : 'غیرفعال';

        if($privacy == 'فعال' && $targetUserId != $from_id) {
            // کاربر حریم خصوصی فعال دارد
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"🔒 این کاربر حریم خصوصی فعال دارد و پروفایل او قابل مشاهده نیست.",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                        ],
                    ]
                ])
            ]);
        } else {
            // نمایش پروفایل کاربر
            $baseNickname = !empty($targetUser['userfild']['nickname']) ? $targetUser['userfild']['nickname'] : "ناشناس";
            $gender = !empty($targetUser['userfild']['gender']) ? $targetUser['userfild']['gender'] : "تنظیم نشده";
            $city = !empty($targetUser['userfild']['city']) ? $targetUser['userfild']['city'] : "تنظیم نشده";
            $age = !empty($targetUser['userfild']['age']) ? $targetUser['userfild']['age']." سال" : "تنظیم نشده";
            $score = isset($targetUser['userfild']['score']) ? $targetUser['userfild']['score'] : "5";
            $coins = isset($targetUser['userfild']['coins']) ? $targetUser['userfild']['coins'] : "20";
            $join_date = !empty($targetUser['userfild']['join_date']) ? $targetUser['userfild']['join_date'] : "نامشخص";

            // بررسی وضعیت VIP کاربر (بررسی اشتراک فعال)
            $isVIP = false;
            if (isset($targetUser['userfild']['subscription']) &&
                isset($targetUser['userfild']['subscription']['is_active']) &&
                $targetUser['userfild']['subscription']['is_active'] &&
                isset($targetUser['userfild']['subscription']['end_date']) &&
                $targetUser['userfild']['subscription']['end_date'] > time()) {
                $isVIP = true;
            }

            // اضافه کردن ایموجی ✨ برای کاربران VIP
            $nickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;
            $vipStatus = $isVIP ? "\n✱ این کاربر VIP است" : "";

            // دریافت توکن کاربر و آخرین بازدید
            $userToken = isset($targetUser['userfild']['user_token']) ? $targetUser['userfild']['user_token'] : "نامشخص";
            $lastVisit = getLastVisitStatus($targetUser);

            // متن کپشن پروفایل
            $profileCaption = "👤 پروفایل کاربر

✱ نام مستعار : $nickname$vipStatus
✱ توکن کاربر : <a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>
✱ آخرین بازدید : $lastVisit
✱ تاریخ ورود : $join_date
✱ جنسیت : $gender
✱ شهر : $city
✱ سن : $age
✱ امتیاز : $score
✱ سکه : $coins";

            // بررسی تصویر ذخیره شده کاربر
            $userPhoto = isset($targetUser['userfild']['photo']) ? $targetUser['userfild']['photo'] : "";

            if (!empty($userPhoto)) {
                // کاربر تصویر ذخیره شده دارد - ارسال تصویر ذخیره شده
                jijibot('sendPhoto', [
                    'chat_id' => $chat_id,
                    'photo' => $userPhoto,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                                ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                            ],
                            [
                                ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                                ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                            ],
                            [
                                ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                                ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                            ],
                            [
                                ['text' => "❌ بستن", 'callback_data' => "close"]
                            ],
                        ]
                    ])
                ]);
            } else {
                // کاربر تصویر ذخیره شده ندارد - استفاده از تصویر پیش‌فرض
                $defaultPhotoUrl = "https://t.me/fjw2w9c9/1855";

                jijibot('sendPhoto', [
                    'chat_id' => $chat_id,
                    'photo' => $defaultPhotoUrl,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                                ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                            ],
                            [
                                ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                                ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                            ],
                            [
                                ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                                ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                            ],
                            [
                                ['text' => "❌ بستن", 'callback_data' => "close"]
                            ],
                        ]
                    ])
                ]);
            }
        }
    } else {
        // توکن نامعتبر
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ توکن کاربری نامعتبر!

لطفا توکن صحیح را وارد کنید.
مثال: /uAbc123X",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    }
} else {
    // فرمت نادرست
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ فرمت دستور نادرست!

برای مشاهده پروفایل کاربر از فرمت زیر استفاده کنید:
/u{توکن 5-10 حرفی}

مثال: /uAbc12 یا /uAbc123XY",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);
}
}
elseif($textmassage=="/game" or $textmassage=="/game@$usernamebot"){
if($tc == "group" or $tc == "supergroup"){
if(count($getgp["gamer"]) < 2){
unset($getgp["gamer"]);
$getgp["gamer"][]="$from_id";
$getgamer = $getgp["gamer"];
for($z = 0;$z <= count($getgamer) - 1;$z++){
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$z]",'user_id'=>"$getgamer[$z]"]);
$name = $stat->result->user->first_name;
$zplus = $z + 1 ;
$ingamer = $ingamer."$zplus - $name"."\n";
}
	jijibot('sendmessage',[
        "chat_id"=>$chat_id,
        "text"=>"🎮 بیاین جرعت حقیقت بازی کنیم

🙃 بازیکنان پایه  : 

$ingamer
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"☑️ شروع بازی",'callback_data'=>"startgame"],['text'=>"✌🏻 من پایه ام",'callback_data'=>"togame"]
	],
			[
	['text'=>"📢 کانال ما",'url'=>"https://t.me/$channel"],['text'=>"دنیا رو بگیر توو دستات",'url'=>"https://t.me/MantraMoods"]
	],
              ]
        ])
		]);
$getgp["creator"]="$from_id";
$getgp = json_encode($getgp,true);
file_put_contents("data/gp/$chat_id.json",$getgp);
}
else
{
	jijibot('sendmessage',[
        "chat_id"=>$chat_id,
        "text"=>"🎮 بازی قبلی هنوز تموم نشده ! امکان ساخت بازی جدید وجود ندارد 
		
📍 ابتدا بازی قبلی را حذف کنید یا ادامه دهید
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"🗑 حذف بازی",'callback_data'=>"removegame"]
	],
              ]
        ])
		]);
}
}
}
elseif($textmassage=="❌ پایان بازی"){
    // بررسی اینکه آیا کاربر در بازی است یا نه
    if(!isset($juser["userfild"]["ingame"]) || $juser["userfild"]["ingame"] != "on") {
        jijibot('sendmessage',[
            "chat_id"=>$chat_id,
            "text"=>"❌ شما در حال حاضر در هیچ بازی نیستید!

برای شروع بازی جدید از منوی اصلی استفاده کنید.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت به منو",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }

    // بررسی اینکه آیا 30 ثانیه از شروع بازی گذشته است
    if (!canEndGame($from_id, 30)) {
        $remainingTime = getRemainingTime($from_id, 30);
        jijibot('sendmessage',[
            "chat_id"=>$chat_id,
            "text"=>"⏰ برای پایان دادن به بازی باید حداقل 30 ثانیه از شروع بازی بگذرد.

⏳ زمان باقی‌مانده: $remainingTime ثانیه

لطفا صبر کنید و سپس دوباره تلاش کنید.",
            'reply_markup'=>json_encode([
                'keyboard'=>[
                    [
                        ['text'=>"👀 مشاهده حریف"],
                        ['text'=>"❌ پایان بازی"]
                    ],
                ],
                'resize_keyboard'=>true
            ])
        ]);
        return;
    }

	jijibot('sendmessage',[
        "chat_id"=>$chat_id,
        "text"=>"⚠️ آیا می خواهید از این بازی خارج شوید؟",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"✅ بله",'callback_data'=>"yes"],['text'=>"❌ خیر",'callback_data'=>"no"]
	],
              ]
        ])
		]);
}
elseif($textmassage=="❌ ترک بازی"){
    if(check_membership($from_id, $channel1, $channel2)){
        // بررسی اینکه کاربر در بازی 4 نفره است
        if(isUserInFourPlayerGame($from_id)) {
            // بررسی زمان بازی - اگر کمتر از 30 ثانیه باشد
            if (!canEndGame($from_id, 30)) {
                $remainingTime = getRemainingTime($from_id, 30);
                jijibot('sendmessage',[
                    "chat_id"=>$chat_id,
                    "text"=>"⏰ برای ترک بازی باید حداقل 30 ثانیه از شروع بازی بگذرد.

⏳ زمان باقی‌مانده: $remainingTime ثانیه

لطفا صبر کنید و سپس دوباره تلاش کنید.",
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👥 مشاهده بازیکنان"],
                                ['text'=>"❌ ترک بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
                return;
            }

            jijibot('sendmessage',[
                "chat_id"=>$chat_id,
                "text"=>"⚠️ آیا می خواهید از بازی 4 نفره خارج شوید؟

🚨 توجه: با خروج شما، بازی برای همه بازیکنان پایان می‌یابد!",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"✅ بله، خروج",'callback_data'=>"leave_four_player_game"],
                            ['text'=>"❌ خیر",'callback_data'=>"cancel_leave_game"]
                        ],
                    ]
                ])
            ]);
        }
        // بررسی اینکه کاربر در بازی 3 نفره است
        elseif(isUserInThreePlayerGame($from_id)) {
            // بررسی زمان بازی - اگر کمتر از 30 ثانیه باشد
            if (!canEndGame($from_id, 30)) {
                $remainingTime = getRemainingTime($from_id, 30);
                jijibot('sendmessage',[
                    "chat_id"=>$chat_id,
                    "text"=>"⏰ برای ترک بازی باید حداقل 30 ثانیه از شروع بازی بگذرد.

⏳ زمان باقی‌مانده: $remainingTime ثانیه

لطفا صبر کنید و سپس دوباره تلاش کنید.",
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👥 مشاهده بازیکنان"],
                                ['text'=>"❌ ترک بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
                return;
            }

            jijibot('sendmessage',[
                "chat_id"=>$chat_id,
                "text"=>"⚠️ آیا می خواهید از بازی 3 نفره خارج شوید؟

🚨 توجه: با خروج شما، بازی برای همه بازیکنان پایان می‌یابد!",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"✅ بله، خروج",'callback_data'=>"leave_three_player_game"],
                            ['text'=>"❌ خیر",'callback_data'=>"cancel_leave_game"]
                        ],
                    ]
                ])
            ]);
        } else {
            jijibot('sendmessage',[
                "chat_id"=>$chat_id,
                "text"=>"❌ شما در حال حاضر در بازی نیستید!",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        }
    } else {
        force_join($from_id, $first_name, $usernamebot);
    }
}
elseif($textmassage=="👀 مشاهده حریف"){
    if(check_membership($from_id, $channel1, $channel2)){
        // به‌روزرسانی آخرین بازدید
        updateLastVisit($from_id);

        // بررسی وجود حریف
        if(!empty($juser["userfild"]["rival"]) && $juser["userfild"]["ingame"] == "on") {
            $rivalId = $juser["userfild"]["rival"];
            $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);

            // تنظیم مقادیر پیش‌فرض
            $nickname = !empty($rivalData['userfild']['nickname']) ? $rivalData['userfild']['nickname'] : "تنظیم نشده";
            $gender = !empty($rivalData['userfild']['gender']) ? $rivalData['userfild']['gender'] : "تنظیم نشده";
            $city = !empty($rivalData['userfild']['city']) ? $rivalData['userfild']['city'] : "تنظیم نشده";
            $age = !empty($rivalData['userfild']['age']) ? $rivalData['userfild']['age'] : "تنظیم نشده";
            $joinDate = !empty($rivalData['userfild']['join_date']) ? $rivalData['userfild']['join_date'] : "نامشخص";
            $userToken = !empty($rivalData['userfild']['user_token']) ? $rivalData['userfild']['user_token'] : "نامشخص";
            $coins = isset($rivalData['userfild']['coins']) ? $rivalData['userfild']['coins'] : 0;
            $score = isset($rivalData['userfild']['score']) ? $rivalData['userfild']['score'] : 0;
            $lastVisit = getLastVisitStatus($rivalData);
            $vipStatus = ""; // فعلاً خالی

            // متن کپشن پروفایل
            $profileCaption = "👤 پروفایل حریف

✱ نام مستعار : $nickname$vipStatus
✱ توکن کاربر : <a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>
✱ آخرین بازدید : $lastVisit
✱ تاریخ ورود : $joinDate
✱ جنسیت : $gender
✱ شهر : $city
✱ سن : $age
✱ امتیاز : $score
✱ سکه : $coins";

            // بررسی تصویر ذخیره شده کاربر
            $userPhoto = isset($rivalData['userfild']['photo']) ? $rivalData['userfild']['photo'] : "";

            if (!empty($userPhoto)) {
                // کاربر تصویر ذخیره شده دارد - ارسال تصویر ذخیره شده
                jijibot('sendPhoto', [
                    'chat_id' => $chat_id,
                    'photo' => $userPhoto,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$rivalId"],
                                ['text' => "💬 ارسال پیام", 'callback_data' => "message_$rivalId"]
                            ],
                            [
                                ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$rivalId"],
                                ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$rivalId"]
                            ],
                            [
                                ['text' => "⚠️ گزارش", 'callback_data' => "report_$rivalId"],
                                ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$rivalId"]
                            ],
                            [
                                ['text' => "❌ بستن", 'callback_data' => "close"]
                            ]
                        ]
                    ])
                ]);
            } else {
                // کاربر تصویر ذخیره شده ندارد - استفاده از تصویر پیش‌فرض
                $defaultPhotoUrl = "https://t.me/fjw2w9c9/1855";

                jijibot('sendPhoto', [
                    'chat_id' => $chat_id,
                    'photo' => $defaultPhotoUrl,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$rivalId"],
                                ['text' => "💬 ارسال پیام", 'callback_data' => "message_$rivalId"]
                            ],
                            [
                                ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$rivalId"],
                                ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$rivalId"]
                            ],
                            [
                                ['text' => "⚠️ گزارش", 'callback_data' => "report_$rivalId"],
                                ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$rivalId"]
                            ],
                            [
                                ['text' => "❌ بستن", 'callback_data' => "close"]
                            ]
                        ]
                    ])
                ]);
            }
        } else {
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"❌ خطا

شما در حال حاضر در بازی نیستید یا حریفی ندارید."
            ]);
        }
    } else {
        force_join($chat_id, $first_name, $usernamebot);
    }
}
elseif($textmassage=="👥 مشاهده بازیکنان"){
    if(check_membership($from_id, $channel1, $channel2)){
        // به‌روزرسانی آخرین بازدید
        updateLastVisit($from_id);

        // بررسی اینکه کاربر در بازی 4 نفره است
        if(isUserInFourPlayerGame($from_id)) {
            $gameId = getUserFourPlayerGameId($from_id);
            $gameData = getFourPlayerGameData($gameId);

            if($gameData) {
                $playersList = "";
                $playerCount = 1;

                foreach($gameData['players'] as $playerId) {
                    $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
                    $nickname = !empty($playerData['userfild']['nickname']) ? $playerData['userfild']['nickname'] : "ناشناس";
                    $userToken = isset($playerData['userfild']['user_token']) ? $playerData['userfild']['user_token'] : "نامشخص";
                    $gender = !empty($playerData['userfild']['gender']) ? $playerData['userfild']['gender'] : "نامشخص";

                    $playersList .= "$playerCount. $nickname (<a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>) - $gender\n";
                    $playerCount++;
                }

                jijibot('sendmessage',[
                    'chat_id'=>$from_id,
                    'text'=>"👥 بازیکنان بازی 4 نفره:

$playersList
🎮 وضعیت بازی: در حال انجام
⏰ شروع بازی: " . date('H:i', $gameData['created_at']),
                    'parse_mode'=>'HTML',
                    'disable_web_page_preview'=>true,
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👥 مشاهده بازیکنان"],
                                ['text'=>"❌ ترک بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
            } else {
                jijibot('sendmessage',[
                    'chat_id'=>$from_id,
                    'text'=>"❌ اطلاعات بازی یافت نشد!",
                    'reply_markup'=>json_encode([
                        'remove_keyboard'=>true
                    ])
                ]);
            }
        }
        // بررسی اینکه کاربر در بازی 3 نفره است
        elseif(isUserInThreePlayerGame($from_id)) {
            $gameId = getUserThreePlayerGameId($from_id);
            $gameData = getThreePlayerGameData($gameId);

            if($gameData) {
                $playersList = "";
                $playerCount = 1;

                foreach($gameData['players'] as $playerId) {
                    $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
                    $nickname = !empty($playerData['userfild']['nickname']) ? $playerData['userfild']['nickname'] : "ناشناس";
                    $userToken = isset($playerData['userfild']['user_token']) ? $playerData['userfild']['user_token'] : "نامشخص";
                    $gender = !empty($playerData['userfild']['gender']) ? $playerData['userfild']['gender'] : "نامشخص";

                    $playersList .= "$playerCount. $nickname (<a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>) - $gender\n";
                    $playerCount++;
                }

                jijibot('sendmessage',[
                    'chat_id'=>$from_id,
                    'text'=>"👥 بازیکنان بازی 3 نفره:

$playersList
🎮 وضعیت بازی: در حال انجام
⏰ شروع بازی: " . date('H:i', $gameData['created_at']),
                    'parse_mode'=>'HTML',
                    'disable_web_page_preview'=>true,
                    'reply_markup'=>json_encode([
                        'keyboard'=>[
                            [
                                ['text'=>"👥 مشاهده بازیکنان"],
                                ['text'=>"❌ ترک بازی"]
                            ],
                        ],
                        'resize_keyboard'=>true
                    ])
                ]);
            } else {
                jijibot('sendmessage',[
                    'chat_id'=>$from_id,
                    'text'=>"❌ اطلاعات بازی یافت نشد!",
                    'reply_markup'=>json_encode([
                        'remove_keyboard'=>true
                    ])
                ]);
            }
        } else {
            jijibot('sendmessage',[
                'chat_id'=>$from_id,
                'text'=>"❌ شما در حال حاضر در بازی نیستید!",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        }
    } else {
        force_join($from_id, $first_name, $usernamebot);
    }
}

elseif($update->message->new_chat_member->id == $botid){
unset($getgp["gamer"]);
$getgp["gamer"][]="$from_id";
$getgamer = $getgp["gamer"];
for($z = 0;$z <= count($getgamer) - 1;$z++){
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$z]",'user_id'=>"$getgamer[$z]"]);
$name = $stat->result->user->first_name;
$zplus = $z + 1 ;
$ingamer = $ingamer."$zplus - $name"."\n";
}
	jijibot('sendmessage',[
        "chat_id"=>$chat_id,
        "text"=>"🎮 بیاین جرعت حقیقت بازی کنیم
		
🙃 بازیکنان پایه  : 

$ingamer
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"☑️ شروع بازی",'callback_data'=>"startgame"],['text'=>"✌🏻 من پایه ام",'callback_data'=>"togame"]
	],
			[
	['text'=>"📢 کانال ما",'url'=>"https://t.me/$channel"],['text'=>"دنیا رو بگیر توو دستات",'url'=>"https://t.me/MantraMoods"]
	],
              ]
        ])
		]);
$getgp["creator"]="$from_id";
$getgp = json_encode($getgp,true);
file_put_contents("data/gp/$chat_id.json",$getgp);
}
elseif($update->message->reply_to_message && $from_id == $Dev[0] && $tc == "private"){
	jijibot('sendmessage',[
        "chat_id"=>$chat_id,
        "text"=>"پیام شما برای فرد ارسال شد ✔️"
		]);
	jijibot('sendmessage',[
        "chat_id"=>$update->message->reply_to_message->forward_from->id,
        "text"=>"👤 پاسخ پشتیبان برای شما :

`$textmassage`",
'parse_mode'=>'MarkDown'
		]);
}
elseif($data=="togame"){
if(check_membership($fromid, $channel1, $channel2)){
$key = array_search($fromid,$getgpc["gamer"]);
if(!is_numeric($key)){
$getgpc["gamer"][]="$fromid";
$getgamer = $getgpc["gamer"];
for($z = 0;$z <= count($getgamer) - 1;$z++){
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$z]",'user_id'=>"$getgamer[$z]"]);
$name = $stat->result->user->first_name;
$zplus = $z + 1 ;
$ingamer = $ingamer."$zplus - $name"."\n";
}
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🎮 بیاین جرعت حقیقت بازی کنیم 
🙃 بازیکنان پایه  : 

$ingamer
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"☑️ شروع بازی",'callback_data'=>"startgame"],['text'=>"✌🏻 من پایه ام",'callback_data'=>"togame"]
	],
		[
	['text'=>"📢 کانال ما",'url'=>"https://t.me/$channel"],['text'=>"دنیا رو بگیر توو دستات",'url'=>"https://t.me/MantraMoods"]
	],
              ]
        ])
	  	]);	
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
    jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 شما قبلا در این بازی حضور داشتید",
            'show_alert' =>true
        ]);
}
}
else
{
     jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
            'show_alert' =>true
        ]);
}
}
elseif($data=="startgame"){
if(check_membership($fromid, $channel1, $channel2)){
$getstats = jijibot('getChatMember',['chat_id'=>"$chatid",'user_id'=>"$fromid"]);
$stats = $getstats->result->status;
if ($stats == 'creator' or $fromid == $getgpc["creator"]) {
if(count($getgpc["gamer"]) >= 2){
$getgamer = $getgpc["gamer"];
$random = array_rand($getgamer);
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$random]",'user_id'=>"$getgamer[$random]"]);
$name = $stat->result->user->first_name;
jijibot('sendmessage',[
                'chat_id'=>$chatid,
	'text'=>"📍 نوبت $name شد ! انتخاب کن ! 
	
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"💪🏻 جرعت",'callback_data'=>"jo"],['text'=>"🗣 حقیقت",'callback_data'=>"ha"]
	],
			[
	['text'=>"🤞🏻 شانسی",'callback_data'=>"random"]
	],
					[
	['text'=>"⏩ نفر بعدی",'callback_data'=>"othergamer"]
	],
              ]
        ])
	  	]);
      jijibot('deletemessage',[
                'chat_id'=>$chatid,
            'message_id'=>$messageid
            ]);
$getgpc["turn"]="$getgamer[$random]";
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
		     jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 تعداد بازی کنان بازی باید دو نفر یا بیش تر باشد",
            'show_alert' =>true
        ]);
}
}
else
{
   jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 شما دست رسی به اغاز بازی را ندارید [تنها برای سازنده گروه یا بازی]",
            'show_alert' =>true
        ]);
}
}
else
{
  jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
            'show_alert' =>true
        ]);

}
}
elseif(in_array($data,array("jo","ha","random"))){
if($getgpc["turn"] == $fromid){
if($data == "random"){
$array = array("ha","jo");
$random = array_rand($array);
$data = "$array[$random]";
}
$replace = str_replace(["jo","ha"],["جرعت رو انتخاب کرد","حقیقت رو انتخاب کرد"],$data);
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🌟 خب $firstname $replace

📍 نوع سوال رو مشخص کن

➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"😊 عادی",'callback_data'=>"normal"],['text'=>"🔞 + 18",'callback_data'=>"plus"]
	],
              ]
        ])
	  	]);
$getgpc["stats"]="$data";
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
 jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 نوبت شما برای انتخاب نیست",
            'show_alert' =>true
        ]);
}
}
elseif(in_array($data,array("normal","plus"))){
if($getgpc["turn"] == $fromid){
if($data == "normal"){
$stats = $getgpc["stats"];
$randomchalange = array_rand($database["$stats"]["$data"]);
$randomch = $database["$stats"]["$data"]["$randomchalange"];
$replace = str_replace(["jo","ha"],["انجام بده","حقیقت رو بگو"],$stats);
$replaces = str_replace(["jo","ha"],["انجام بدی","حقیقت رو بگی"],$stats);
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🌟 خب $firstname $replace

📍 $randomch

➖➖➖➖➖➖➖➖

📍 5 دقیقه فرصت داری $replaces",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"☑️ عمل کرد",'callback_data'=>"okkard"],['text'=>"❌ عمل نکرد",'callback_data'=>"oknakard"]
	],
              ]
        ])
	  	]);
}
else
{
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🌟 خب $firstname

📍 جنسیت خودت رو انتخاب کن

➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"🤴🏻 پسر",'callback_data'=>"boy"],['text'=>"👸🏻 دختر",'callback_data'=>"girl"]
	],
              ]
        ])
	  	]);
}
}
else
{
 jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 نوبت شما برای انتخاب نیست",
            'show_alert' =>true
        ]);
}
}
elseif(in_array($data,array("boy","girl"))){
if($getgpc["turn"] == $fromid){
$stats = $getgpc["stats"];
$randomchalange = array_rand($database["$stats"]["plus"]["$data"]);
$randomch = $database["$stats"]["plus"]["$data"]["$randomchalange"];
$replace = str_replace(["jo","ha"],["انجام بده","حقیقت رو بگو"],$stats);
$replaces = str_replace(["jo","ha"],["انجام بدی","حقیقت رو بگی"],$stats);
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🌟 خب $firstname $replace

📍 $randomch

➖➖➖➖➖➖➖➖

📍 5 دقیقه فرصت داری $replaces",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"☑️ عمل کرد",'callback_data'=>"okkard"],['text'=>"❌ عمل نکرد",'callback_data'=>"oknakard"]
	],
              ]
        ])
	  	]);
}
else
{
 jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 نوبت شما برای انتخاب نیست",
            'show_alert' =>true
        ]);
}
}
elseif($data=="okkard"){
$getstats = jijibot('getChatMember',['chat_id'=>"$chatid",'user_id'=>"$fromid"]);
$stats = $getstats->result->status;
if ($stats == 'creator' or $fromid == $getgpc["creator"]) {
$replace = str_replace(["jo","ha"],["انجام داد","حقیقت رو گفت"],$getgpc["stats"]);
$turn = $getgpc["turn"];
$statturn = jijibot('getChatMember',['chat_id'=>"$turn",'user_id'=>"$turn"]);
$nameturn = $statturn->result->user->first_name;
$getgamer = $getgpc["gamer"];
$random = array_rand($getgamer);
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$random]",'user_id'=>"$getgamer[$random]"]);
$name = $stat->result->user->first_name;
jijibot('sendmessage',[
                'chat_id'=>$chatid,
	'text'=>"📍 خوب خوب ! $nameturn $replace
	
📍 نوبت $name شد ! انتخاب کن ! 
	
➖➖➖➖➖➖➖➖
👮🏻 داور بازی : @$usernameca",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"💪🏻 جرعت",'callback_data'=>"jo"],['text'=>"🗣 حقیقت",'callback_data'=>"ha"]
	],
			[
	['text'=>"🤞🏻 شانسی",'callback_data'=>"random"]
	],
					[
	['text'=>"⏩ نفر بعدی",'callback_data'=>"othergamer"]
	],
              ]
        ])
	  	]);
		      jijibot('deletemessage',[
                'chat_id'=>$chatid,
            'message_id'=>$messageid
            ]);
$getgpc["turn"]="$getgamer[$random]";
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
  jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 شما دست رسی به داوری بازی را ندارید [تنها برای سازنده گروه یا بازی]",
            'show_alert' =>true
        ]);
}
}
elseif($data=="oknakard"){
$getstats = jijibot('getChatMember',['chat_id'=>"$chatid",'user_id'=>"$fromid"]);
$stats = $getstats->result->status;
if ($stats == 'creator' or $fromid == $getgpc["creator"]) {
$turn = $getgpc["turn"];
$key = array_search($turn,$getgpc["gamer"]);
unset($getgpc["gamer"][$key]);
$getgpc["gamer"] = array_values($getgpc["gamer"]); 
$replace = str_replace(["jo","ha"],["انجام نداد","حقیقت رو نگفت"],$getgpc["stats"]);
if(count($getgpc["gamer"]) >= 2){
$statturn = jijibot('getChatMember',['chat_id'=>"$turn",'user_id'=>"$turn"]);
$nameturn = $statturn->result->user->first_name;
$getgamer = $getgpc["gamer"];
$random = array_rand($getgamer);
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$random]",'user_id'=>"$getgamer[$random]"]);
$name = $stat->result->user->first_name;
jijibot('sendmessage',[
                'chat_id'=>$chatid,
	'text'=>"📍 خوب خوب ! $nameturn $replace 
🎈 از بازی حذف شد
	
📍 نوبت $name شد ! انتخاب کن ! 
	
➖➖➖➖➖➖➖➖
👮🏻 داور بازی : @$usernameca",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"💪🏻 جرعت",'callback_data'=>"jo"],['text'=>"🗣 حقیقت",'callback_data'=>"ha"]
	],
			[
	['text'=>"🤞🏻 شانسی",'callback_data'=>"random"]
	],
				[
	['text'=>"⏩ نفر بعدی",'callback_data'=>"othergamer"]
	],
              ]
        ])
	  	]);
		      jijibot('deletemessage',[
                'chat_id'=>$chatid,
            'message_id'=>$messageid
            ]);
$getgpc["turn"]="$getgamer[$random]";
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
$gamer = $getgpc["gamer"][0];
$statgamer = jijibot('getChatMember',['chat_id'=>"$gamer",'user_id'=>"$gamer"]);
$namegamer = $statgamer->result->user->first_name;
$statturn = jijibot('getChatMember',['chat_id'=>"$turn",'user_id'=>"$turn"]);
$nameturn = $statturn->result->user->first_name;
jijibot('sendmessage',[
                'chat_id'=>$chatid,
	'text'=>"❄️ خوب خوب ! $nameturn $replace 
🎈 از بازی حذف شد
	
📍 تعداد بازیکنان باقی مانده این بازی به حداقل رسیده 
	
🌟 برنده بازی : $namegamer
🎈 برای شروع دوباره بازی /game را ارسال کنید",
	  	]);
		      jijibot('deletemessage',[
                'chat_id'=>$chatid,
            'message_id'=>$messageid
            ]);
unset($getgpc["gamer"]);
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
}
else
{
  jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 شما دست رسی به داوری بازی را ندارید [تنها برای سازنده گروه یا بازی]",
            'show_alert' =>true
        ]);
}
}
elseif($data=="othergamer"){
$getstats = jijibot('getChatMember',['chat_id'=>"$chatid",'user_id'=>"$fromid"]);
$stats = $getstats->result->status;
if ($stats == 'creator' or $fromid == $getgpc["creator"]) {
$getgamer = $getgpc["gamer"];
$random = array_rand($getgamer);
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$random]",'user_id'=>"$getgamer[$random]"]);
$name = $stat->result->user->first_name;
jijibot('sendmessage',[
                'chat_id'=>$chatid,
	'text'=>"📍 نوبت $name شد ! انتخاب کن ! 
	
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"💪🏻 جرعت",'callback_data'=>"jo"],['text'=>"🗣 حقیقت",'callback_data'=>"ha"]
	],
			[
	['text'=>"🤞🏻 شانسی",'callback_data'=>"random"]
	],
					[
	['text'=>"⏩ نفر بعدی",'callback_data'=>"othergamer"]
	],
              ]
        ])
	  	]);
      jijibot('deletemessage',[
                'chat_id'=>$chatid,
            'message_id'=>$messageid
            ]);
$getgpc["turn"]="$getgamer[$random]";
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
  jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 شما دست رسی به تعویض بازیکن را ندارید [تنها برای سازنده گروه یا بازی]",
            'show_alert' =>true
        ]);
}
}
elseif($data=="removegame"){
$getstats = jijibot('getChatMember',['chat_id'=>"$chatid",'user_id'=>"$fromid"]);
$stats = $getstats->result->status;
if ($stats == 'creator' or $stats == 'administrator' or $fromid == $getgpc["creator"]) {
unset($getgpc["gamer"]);
$getgpc["gamer"][]="$fromid";
$getgamer = $getgpc["gamer"];
for($z = 0;$z <= count($getgamer) - 1;$z++){
$stat = jijibot('getChatMember',['chat_id'=>"$getgamer[$z]",'user_id'=>"$getgamer[$z]"]);
$name = $stat->result->user->first_name;
$zplus = $z + 1 ;
$ingamer = $ingamer."$zplus - $name"."\n";
}
	jijibot('sendmessage',[
        "chat_id"=>$chatid,
        "text"=>"🎮 بیاین جرعت حقیقت بازی کنیم
		
🙃 بازیکنان پایه  : 

$ingamer
➖➖➖➖➖➖➖➖➖",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"☑️ شروع بازی",'callback_data'=>"startgame"],['text'=>"✌🏻 من پایه ام",'callback_data'=>"togame"]
	],
			[
	['text'=>"📢 کانال ما",'url'=>"https://t.me/$channel"],['text'=>"دنیا رو بگیر توو دستات",'url'=>"https://t.me/MantraMoods"]
	],
              ]
        ])
		]);
      jijibot('deletemessage',[
                'chat_id'=>$chatid,
            'message_id'=>$messageid
            ]);
$getgpc["creator"]="$fromid";
$getgpc = json_encode($getgpc,true);
file_put_contents("data/gp/$chatid.json",$getgpc);
}
else
{
  jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "📍 شما دست رسی به حذف بازی را ندارید [تنها برای ادمین ها و سازنده گروه یا بازی]",
            'show_alert' =>true
        ]);
}
}
elseif($data=="check_membership"){
// نمایش پیام در حال بررسی
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"⌛️ در حال بررسی عضویت شما...",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔔 عضویت در کانال دوم",'url'=>"https://t.me/speedxteam"],
                ['text'=>"🔔 عضویت در کانال اول",'url'=>"https://t.me/speedx_bots"]
            ],
            [
                ['text'=>"🔄 بررسی مجدد عضویت",'callback_data'=>"check_membership"]
            ]
        ]
    ])
]);

// تاخیر 1 ثانیه
sleep(1);

// بررسی عضویت
if(!check_membership($fromid, $channel1, $channel2)){
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"❌ شما هنوز عضو نیستید!

برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔔 عضویت در کانال دوم",'url'=>"https://t.me/speedxteam"],
                    ['text'=>"🔔 عضویت در کانال اول",'url'=>"https://t.me/speedx_bots"]
                ],
                [
                    ['text'=>"🔄 بررسی مجدد عضویت",'callback_data'=>"check_membership"]
                ]
            ]
        ])
    ]);
}
else
{
// بررسی وجود فایل کاربر و ایجاد آن در صورت عدم وجود
if(!file_exists("data/user/$fromid.json")){
// ایجاد ساختار اولیه کاربر جدید
$new_user = array(
    "userfild" => array(
        "step" => "none",
        "ingame" => "off",
        "rival" => "",
        "nickname" => "",
        "gender" => "",
        "city" => "",
        "age" => "",
        "privacy" => "غیرفعال",
        "join_date" => get_current_jalali_date(),
        "phone" => "",
        "photo" => "",
        "coins" => 20,
        "score" => 5,
        "user_token" => generateUniqueUserToken(),
        "last_visit" => get_current_jalali_date()
    )
);
$new_user = json_encode($new_user, true);
file_put_contents("data/user/$fromid.json", $new_user);
}

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"سلام $firstname 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
	['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
	],
	[
	['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
	['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
	],
			[
	['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
	],
			[
	['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
	],
		[
	['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
	],
		[
	['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
	],
              ]
        ])
  	]);
}
}
elseif($data=="join"){
if(!check_membership($fromid, $channel1, $channel2)){
       jijibot('answercallbackquery', [
            'callback_query_id' =>$membercall,
            'text' => "❌ هنوز در هر دو کانال عضو نیستید",
            'show_alert' =>true
        ]);
}
else
{
// بررسی وجود فایل کاربر و ایجاد آن در صورت عدم وجود
if(!file_exists("data/user/$fromid.json")){
// ایجاد ساختار اولیه کاربر جدید
$new_user = array(
    "userfild" => array(
        "step" => "none",
        "ingame" => "off",
        "rival" => "",
        "nickname" => "",
        "gender" => "",
        "city" => "",
        "age" => "",
        "privacy" => "غیرفعال",
        "join_date" => get_current_jalali_date(),
        "phone" => "",
        "photo" => "",
        "coins" => 20,
        "score" => 5,
        "user_token" => generateUniqueUserToken()
    )
);
$new_user = json_encode($new_user, true);
file_put_contents("data/user/$fromid.json", $new_user);
}

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"سلام $firstname 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
	['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
	],
	[
	['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
	['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
	],
			[
	['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
	],
			[
	['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
	],
		[
	['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
	],
		[
	['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
	],
              ]
        ])
	  	]);
}
}
elseif($data=="gamebylink"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// بررسی موجودی سکه کاربر (فقط برای کاربران بدون اشتراک)
if (!hasActiveSubscription($fromid)) {
    $userCoins = isset($cuser['userfild']['coins']) ? $cuser['userfild']['coins'] : 20;
    if ($userCoins < 2) {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ سکه کافی ندارید!

برای ایجاد بازی دوستانه نیاز به 2 سکه دارید.
موجودی فعلی شما: $userCoins سکه

می‌توانید از طریق دکمه‌های زیر سکه دریافت کنید:",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🎁 کد هدیه",'callback_data'=>"gift_code"],
                        ['text'=>"🪙 سکه روزانه",'callback_data'=>"daily_coin"]
                    ],
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }
}

// بررسی وجود نام مستعار
if(empty($cuser["userfild"]["nickname"])) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"📋 قوانین و مقررات ربات

⚠️ برای ایجاد بازی دوستانه ابتدا باید قوانین زیر را مطالعه و تایید کنید:

🔸 از کلمات رکیک و توهین آمیز استفاده نکنید
🔸 محتوای غیراخلاقی و نامناسب ارسال نکنید
🔸 از اسپم و ارسال پیام‌های تکراری خودداری کنید
🔸 به سایر کاربران احترام بگذارید
🔸 از ربات برای اهداف غیرقانونی استفاده نکنید
🔸 اطلاعات شخصی خود و دیگران را فاش نکنید

⚖️ هرگونه فعالیت انجام شده اعم از متن و یا سایر محتوا های ارسالی کاملا به شخص مربوط شده و ربات در این مورد صلب مسیولیت می کند.

⚡️ نقض قوانین منجر به مسدود شدن حساب کاربری شما خواهد شد.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"✅ قوانین را تایید می‌کنم",'callback_data'=>"accept_rules_for_gamebylink"]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // تنظیم step برای تایید قوانین
    $cuser["userfild"]["step"] = "accept_rules_for_gamebylink";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    return;
}

// کسر سکه از کاربر
if (!checkAndDeductCoins($fromid, 2)) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"❌ خطا در کسر سکه! لطفا دوباره تلاش کنید.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);
    return;
}

// تولید کد رندوم برای بازی
$gameCode = generateRandomGameCode();
saveGameCode($fromid, $gameCode);

// تعیین پیام بر اساس وضعیت اشتراک
$coinMessage = hasActiveSubscription($fromid) ?
    "✨ به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشد." :
    "💰 2 سکه از حساب شما کسر شد.";

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"✨ لینک دعوت شما با موفقیت ساخته شد.

$coinMessage

✱  لینک دعوت شما :

<blockquote>https://t.me/$usernamebot?start=p$gameCode</blockquote>

✱  شما میتوانید با اشتراک گذاری لینک خود دوستانتان را به بازی دعوت کنید.
✱  مدت اعتبار این لینک : 24 ساعت
✱ از طریق دکمه زیر می توانید لینک را به اشتراک بگذارید.",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"📤  اشتراک گذاری",'switch_inline_query'=>"invites you\n🎮 جرعت حقیقت را با امنیت و سرعت بالا بازی کن!\n✱ شما میتوانید همین حالا از طریق لینک زیر بازی را با دوست خود شروع کنید.\ntelegram.me/$usernamebot?start=p$gameCode"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
	  	]);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="gamerandom"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// جلوگیری از اجرای مکرر
$currentTime = time();
if(isset($cuser["userfild"]["last_menu_action"]) && ($currentTime - $cuser["userfild"]["last_menu_action"]) < 2) {
    return;
}
$cuser["userfild"]["last_menu_action"] = $currentTime;

// بررسی تایید قوانین
if(!isset($cuser["userfild"]["rules_accepted"]) || $cuser["userfild"]["rules_accepted"] !== true) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"📋 قوانین و مقررات ربات

⚠️ برای شروع بازی ابتدا باید قوانین زیر را مطالعه و تایید کنید:

🔸 از کلمات رکیک و توهین آمیز استفاده نکنید
🔸 محتوای غیراخلاقی و نامناسب ارسال نکنید
🔸 از اسپم و ارسال پیام‌های تکراری خودداری کنید
🔸 به سایر کاربران احترام بگذارید
🔸 از ربات برای اهداف غیرقانونی استفاده نکنید
🔸 اطلاعات شخصی خود و دیگران را فاش نکنید

⚖️ هرگونه فعالیت انجام شده اعم از متن و یا سایر محتوا های ارسالی کاملا به شخص مربوط شده و ربات در این مورد صلب مسیولیت می کند.

⚡️ نقض قوانین منجر به مسدود شدن حساب کاربری شما خواهد شد.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"✅ قوانین را تایید می‌کنم",'callback_data'=>"accept_rules_for_gamerandom"]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // تنظیم step برای تایید قوانین
    $cuser["userfild"]["step"] = "accept_rules_for_gamerandom";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    return;
}

// بررسی وجود نام مستعار
if(empty($cuser["userfild"]["nickname"])) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"⚠️ برای شروع بازی ابتدا باید نام مستعار خود را تنظیم کنید.

لطفا نام مستعار خود را ارسال کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // تنظیم step برای دریافت نام مستعار
    $cuser["userfild"]["step"] = "set_nickname_for_gamerandom";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    return;
}

// بررسی موجودی سکه کاربر (فقط برای کاربران بدون اشتراک)
if (!hasActiveSubscription($fromid)) {
    $userCoins = isset($cuser['userfild']['coins']) ? $cuser['userfild']['coins'] : 20;
    if ($userCoins < 2) {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ سکه کافی ندارید!

برای شروع بازی نیاز به 2 سکه دارید.
موجودی فعلی شما: $userCoins سکه

می‌توانید از طریق دکمه‌های زیر سکه دریافت کنید:",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🎁 کد هدیه",'callback_data'=>"gift_code"],
                        ['text'=>"🪙 سکه روزانه",'callback_data'=>"daily_coin"]
                    ],
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }
}

// نمایش انتخاب نوع بازی
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🎮 انتخاب نوع بازی

با چه کسی می‌خواهید بازی کنید؟",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🎲 رندوم",'callback_data'=>"game_type_random"]
            ],
            [
                ['text'=>"🙋‍♂️ پسر",'callback_data'=>"game_type_boy"],
                ['text'=>"🙋‍♀️ دختر",'callback_data'=>"game_type_girl"]
            ],
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="game_type_random"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// ذخیره نوع بازی انتخابی
$cuser["userfild"]["game_type"] = "random";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// نمایش انتخاب تعداد بازیکن
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🎮 انتخاب تعداد بازیکن

چند نفره می‌خواهید بازی کنید؟",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"👤 انفرادی",'callback_data'=>"game_players_1"]
            ],
            [
                ['text'=>"👥 4 نفره",'callback_data'=>"game_players_4"],
                ['text'=>"👥 3 نفره",'callback_data'=>"game_players_3"]
            ],
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
            ],
        ]
    ])
]);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="no"){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🎮 خوب پس ! به بازیت ادامه بده",
	  	]);
}
elseif($data=="yes"){
	// حذف پیام "آیا می خواهید از این بازی خارج شوید؟"
	jijibot('deletemessage',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid
    ]);

    // بررسی زمان بازی - اگر بیشتر از 2 دقیقه باشد، سکه بازگشت نخورد
    $shouldRefundCoins = !isGameLongerThan2Minutes($fromid);

    if ($shouldRefundCoins) {
        // بازگرداندن سکه به هر دو کاربر (فقط اگر اشتراک ندارند)
        if (!hasActiveSubscription($fromid)) {
            refundCoins($fromid, 2);
        }
        if (!hasActiveSubscription($cuser["userfild"]["rival"])) {
            refundCoins($cuser["userfild"]["rival"], 2);
        }

        // ارسال پیام اول - بازگشت سکه یا اطلاع‌رسانی اشتراک
        if (hasActiveSubscription($fromid)) {
            jijibot('sendmessage',[
                "chat_id"=>$chatid,
                "text"=>"✨ اشتراک ویژه

به دلیل داشتن اشتراک ویژه، هیچ سکه‌ای از شما کسر نشده بود.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        } else {
            jijibot('sendmessage',[
                "chat_id"=>$chatid,
                "text"=>"💳 سکه

به دلیل اتمام بازی 2 سکه که برای این بازی مصرف شده بود بازگردانده شد.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        }

        // ارسال پیام اول برای حریف - بازگشت سکه یا اطلاع‌رسانی اشتراک
        if (hasActiveSubscription($cuser["userfild"]["rival"])) {
            jijibot('sendmessage',[
                "chat_id"=>$cuser["userfild"]["rival"],
                "text"=>"✨ اشتراک ویژه

به دلیل داشتن اشتراک ویژه، هیچ سکه‌ای از شما کسر نشده بود.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        } else {
            jijibot('sendmessage',[
                "chat_id"=>$cuser["userfild"]["rival"],
                "text"=>"💳 سکه

به دلیل اتمام بازی 2 سکه که برای این بازی مصرف شده بود بازگردانده شد.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        }
    } else {
        // عدم بازگشت سکه - ارسال پیام توضیحی
        if (hasActiveSubscription($fromid)) {
            jijibot('sendmessage',[
                "chat_id"=>$chatid,
                "text"=>"✨ اشتراک ویژه

به دلیل داشتن اشتراک ویژه، هیچ سکه‌ای از شما کسر نشده بود.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        } else {
            jijibot('sendmessage',[
                "chat_id"=>$chatid,
                "text"=>"💳 سکه

به دلیل اینکه بازی بیشتر از 2 دقیقه طول کشیده است، سکه‌ها بازگردانده نمی‌شوند.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        }

        // ارسال پیام برای حریف - عدم بازگشت سکه
        if (hasActiveSubscription($cuser["userfild"]["rival"])) {
            jijibot('sendmessage',[
                "chat_id"=>$cuser["userfild"]["rival"],
                "text"=>"✨ اشتراک ویژه

به دلیل داشتن اشتراک ویژه، هیچ سکه‌ای از شما کسر نشده بود.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        } else {
            jijibot('sendmessage',[
                "chat_id"=>$cuser["userfild"]["rival"],
                "text"=>"💳 سکه

به دلیل اینکه بازی بیشتر از 2 دقیقه طول کشیده است، سکه‌ها بازگردانده نمی‌شوند.",
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);
        }
    }

    // ارسال پیام دوم - پایان بازی
    jijibot('sendmessage',[
        "chat_id"=>$chatid,
        "text"=>"🎮 شما بازی را به پایان دادید !

می توانید از طریق دکمه زیر مجدد به منوی اصلی ربات بازگردید.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                ],
            ]
        ])
    ]);

    // ارسال پیام دوم برای حریف - پایان بازی
    jijibot('sendmessage',[
        "chat_id"=>$cuser["userfild"]["rival"],
        "text"=>"🎮 بازی به درخواست طرف مقابل به پایان رسید !

می توانید از طریق دکمه زیر مجدد به منوی اصلی ربات بازگردید.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                ],
            ]
        ])
    ]);

// ابتدا شناسه حریف را ذخیره می‌کنیم
$userrival = $cuser["userfild"]["rival"];

// سپس اطلاعات کاربر فعلی را به‌روزرسانی می‌کنیم
$cuser["userfild"]["step"]="none";
$cuser["userfild"]["ingame"]="off";
$cuser["userfild"]["rival"]="";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);

// و در نهایت اطلاعات حریف را به‌روزرسانی می‌کنیم
$getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
$getrival["userfild"]["step"]="none";
$getrival["userfild"]["ingame"]="off";
$getrival["userfild"]["rival"]="";
$getrival = json_encode($getrival,true);
file_put_contents("data/user/$userrival.json",$getrival);
}
elseif($data=="jorats"){
// ارسال پیام به فرد سوال‌کننده با دکمه‌های انتخاب
jijibot('sendmessage',[
	'chat_id'=>$cuser["userfild"]["rival"],
	'text'=>"💪🏻 جرعت

✱ حریف شما جرعت را انتخاب کرد.
✱ شما می‌توانید از دکمه‌های زیر سوال آماده انتخاب کنید یا سوال دلخواه خود را تایپ کنید:",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
			[
				['text'=>"💪🏻 جرعت",'callback_data'=>"dare_normal"],
				['text'=>"💪🏻 جرعت +18",'callback_data'=>"dare_plus18"]
			],
		]
	])
]);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⏳ انتخاب شد

لطفا منتظر ارسال سوال توسط حریف باشید.",
	  	]);
$userrival = $cuser["userfild"]["rival"];
$getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
$getrival["userfild"]["step"]="game";
$getrival = json_encode($getrival,true);
file_put_contents("data/user/$userrival.json",$getrival);
}
elseif($data=="haghights"){
// ارسال پیام به فرد سوال‌کننده با دکمه‌های انتخاب
jijibot('sendmessage',[
	'chat_id'=>$cuser["userfild"]["rival"],
	'text'=>"📘 حقیقت

✱ حریف شما حقیقت را انتخاب کرد.
✱ شما می‌توانید از دکمه‌های زیر سوال آماده انتخاب کنید یا سوال دلخواه خود را تایپ کنید:",
	'reply_markup'=>json_encode([
		'inline_keyboard'=>[
			[
				['text'=>"📘 حقیقت",'callback_data'=>"truth_normal"],
				['text'=>"📘 حقیقت +18",'callback_data'=>"truth_plus18"]
			],
		]
	])
]);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⏳ انتخاب شد

لطفا منتظر ارسال سوال توسط حریف باشید.",
	  	]);
$userrival = $cuser["userfild"]["rival"];
$getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
$getrival["userfild"]["step"]="game";
$getrival = json_encode($getrival,true);
file_put_contents("data/user/$userrival.json",$getrival);
}
elseif($data=="truth_normal"){
if(check_membership($fromid, $channel1, $channel2)){
    // دریافت سوال تصادفی حقیقت معمولی
    $question = getRandomTruthQuestion('normal');

    // ارسال سوال به فرد پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id'=>$cuser["userfild"]["rival"],
        'text'=>"📘 سوال حقیقت:

$question

✱ حریف از سوالات آماده ربات استفاده کرد.
✱ لطفا پاسخ خود را ارسال کنید.",
    ]);

    // ویرایش پیام فرد سوال‌کننده
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"✅ سوال ارسال شد

📘 سوال حقیقت که برای حریف ارسال شد:
$question

منتظر پاسخ او باشید.",
    ]);

    // تنظیم step فرد پاسخ‌دهنده برای دریافت پاسخ
    $rivalId = $cuser["userfild"]["rival"];
    $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);
    $rivalData["userfild"]["step"] = "answergame";
    $rivalData = json_encode($rivalData, true);
    file_put_contents("data/user/$rivalId.json", $rivalData);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="truth_plus18"){
if(check_membership($fromid, $channel1, $channel2)){
    // دریافت جنسیت فرد پاسخ‌دهنده برای انتخاب سوال مناسب
    $rivalId = $cuser["userfild"]["rival"];
    $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);
    $rivalGender = isset($rivalData['userfild']['gender']) ?
        ($rivalData['userfild']['gender'] == 'دختر' ? 'girl' : 'boy') : null;

    // دریافت سوال تصادفی حقیقت +18
    $question = getRandomTruthQuestion('plus18', $rivalGender);

    // ارسال سوال به فرد پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id'=>$cuser["userfild"]["rival"],
        'text'=>"📘 سوال حقیقت +18:

$question

✱ حریف از سوالات آماده ربات استفاده کرد.
✱ لطفا پاسخ خود را ارسال کنید.",
    ]);

    // ویرایش پیام فرد سوال‌کننده
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"✅ سوال ارسال شد

📘 سوال حقیقت +18 که برای حریف ارسال شد:
$question

منتظر پاسخ او باشید.",
    ]);

    // تنظیم step فرد پاسخ‌دهنده برای دریافت پاسخ
    $rivalId = $cuser["userfild"]["rival"];
    $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);
    $rivalData["userfild"]["step"] = "answergame";
    $rivalData = json_encode($rivalData, true);
    file_put_contents("data/user/$rivalId.json", $rivalData);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="dare_normal"){
if(check_membership($fromid, $channel1, $channel2)){
    // دریافت چالش تصادفی جرعت معمولی
    $challenge = getRandomDareChallenge('normal');

    // ارسال چالش به فرد پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id'=>$cuser["userfild"]["rival"],
        'text'=>"💪🏻 چالش جرعت:

$challenge

✱ حریف از چالش‌های آماده ربات استفاده کرد.
✱ لطفا چالش را انجام دهید و نتیجه را ارسال کنید.",
    ]);

    // ویرایش پیام فرد سوال‌کننده
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"✅ چالش ارسال شد

💪🏻 چالش جرعت که برای حریف ارسال شد:
$challenge

منتظر انجام چالش توسط او باشید.",
    ]);

    // تنظیم step فرد پاسخ‌دهنده برای دریافت پاسخ
    $rivalId = $cuser["userfild"]["rival"];
    $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);
    $rivalData["userfild"]["step"] = "answergame";
    $rivalData = json_encode($rivalData, true);
    file_put_contents("data/user/$rivalId.json", $rivalData);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="dare_plus18"){
if(check_membership($fromid, $channel1, $channel2)){
    // دریافت جنسیت فرد پاسخ‌دهنده برای انتخاب چالش مناسب
    $rivalId = $cuser["userfild"]["rival"];
    $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);
    $rivalGender = isset($rivalData['userfild']['gender']) ?
        ($rivalData['userfild']['gender'] == 'دختر' ? 'girl' : 'boy') : null;

    // دریافت چالش تصادفی جرعت +18
    $challenge = getRandomDareChallenge('plus18', $rivalGender);

    // ارسال چالش به فرد پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id'=>$cuser["userfild"]["rival"],
        'text'=>"💪🏻 چالش جرعت +18:

$challenge

✱ حریف از چالش‌های آماده ربات استفاده کرد.
✱ لطفا چالش را انجام دهید و نتیجه را ارسال کنید.",
    ]);

    // ویرایش پیام فرد سوال‌کننده
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"✅ چالش ارسال شد

💪🏻 چالش جرعت +18 که برای حریف ارسال شد:
$challenge

منتظر انجام چالش توسط او باشید.",
    ]);

    // تنظیم step فرد پاسخ‌دهنده برای دریافت پاسخ
    $rivalId = $cuser["userfild"]["rival"];
    $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);
    $rivalData["userfild"]["step"] = "answergame";
    $rivalData = json_encode($rivalData, true);
    file_put_contents("data/user/$rivalId.json", $rivalData);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="back"){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"سلام $firstname 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
	['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
	],
	[
	['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
	['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
	],
			[
	['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
	],
			[
	['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
	],
		[
	['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
	],
		[
	['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
	],
              ]
        ])
	  	]);
$cuser["userfild"]["step"]="none";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
elseif($data=="end_game"){
    // بررسی اینکه آیا کاربر در بازی است یا نه
    if(!isset($cuser["userfild"]["ingame"]) || $cuser["userfild"]["ingame"] != "on") {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ شما در حال حاضر در هیچ بازی نیستید!

برای شروع بازی جدید از منوی اصلی استفاده کنید.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت به منو",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }

    // بررسی اینکه آیا 30 ثانیه از شروع بازی گذشته است
    if (!canEndGame($fromid, 30)) {
        $remainingTime = getRemainingTime($fromid, 30);
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"⏰ برای پایان دادن به بازی باید حداقل 30 ثانیه از شروع بازی بگذرد.

⏳ زمان باقی‌مانده: $remainingTime ثانیه

لطفا صبر کنید و سپس دوباره تلاش کنید.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"👀 مشاهده حریف",'callback_data'=>"view_rival"],
                        ['text'=>"❌ پایان بازی",'callback_data'=>"end_game"]
                    ],
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
                    ],
                ]
            ])
        ]);
        return;
    }

    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"⚠️ آیا می خواهید از این بازی خارج شوید؟",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"✅ بله",'callback_data'=>"yes"],['text'=>"❌ خیر",'callback_data'=>"no"]
                ],
            ]
        ])
    ]);
}
elseif($data=="view_rival"){
    // بررسی اینکه آیا کاربر در بازی است یا نه
    if(!isset($cuser["userfild"]["ingame"]) || $cuser["userfild"]["ingame"] != "on") {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ شما در حال حاضر در هیچ بازی نیستید!

برای شروع بازی جدید از منوی اصلی استفاده کنید.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت به منو",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }

    // بررسی وجود حریف
    if(!empty($cuser["userfild"]["rival"])) {
        $rivalId = $cuser["userfild"]["rival"];
        $rivalData = json_decode(file_get_contents("data/user/$rivalId.json"), true);

        // تنظیم مقادیر پیش‌فرض
        $nickname = !empty($rivalData['userfild']['nickname']) ? $rivalData['userfild']['nickname'] : "تنظیم نشده";
        $gender = !empty($rivalData['userfild']['gender']) ? $rivalData['userfild']['gender'] : "تنظیم نشده";
        $city = !empty($rivalData['userfild']['city']) ? $rivalData['userfild']['city'] : "تنظیم نشده";
        $age = !empty($rivalData['userfild']['age']) ? $rivalData['userfild']['age'] : "تنظیم نشده";
        $joinDate = !empty($rivalData['userfild']['join_date']) ? $rivalData['userfild']['join_date'] : "نامشخص";
        $userToken = !empty($rivalData['userfild']['user_token']) ? $rivalData['userfild']['user_token'] : "نامشخص";
        $coins = isset($rivalData['userfild']['coins']) ? $rivalData['userfild']['coins'] : 0;
        $score = isset($rivalData['userfild']['score']) ? $rivalData['userfild']['score'] : 0;
        $lastVisit = getLastVisitStatus($rivalData);
        $vipStatus = ""; // فعلاً خالی

        // متن کپشن پروفایل
        $profileCaption = "👤 پروفایل حریف

✱ نام مستعار : $nickname$vipStatus
✱ توکن کاربر : <a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>
✱ آخرین بازدید : $lastVisit
✱ تاریخ ورود : $joinDate
✱ جنسیت : $gender
✱ شهر : $city
✱ سن : $age
✱ امتیاز : $score
✱ سکه : $coins";

        // بررسی تصویر ذخیره شده کاربر
        $userPhoto = isset($rivalData['userfild']['photo']) ? $rivalData['userfild']['photo'] : "";

        if (!empty($userPhoto)) {
            // کاربر تصویر ذخیره شده دارد - ارسال تصویر ذخیره شده
            jijibot('editMessageMedia', [
                'chat_id' => $chatid,
                'message_id' => $messageid,
                'media' => json_encode([
                    'type' => 'photo',
                    'media' => $userPhoto,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML'
                ]),
                'reply_markup' => json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => "👀 مشاهده حریف", 'callback_data' => "view_rival"],
                            ['text' => "❌ پایان بازی", 'callback_data' => "end_game"]
                        ],
                        [
                            ['text' => "🔙 بازگشت", 'callback_data' => "gamerandom"]
                        ],
                    ]
                ])
            ]);
        } else {
            // کاربر تصویر ندارد - ارسال متن ساده
            jijibot('editmessagetext',[
                'chat_id' => $chatid,
                'message_id' => $messageid,
                'text' => $profileCaption,
                'parse_mode' => 'HTML',
                'disable_web_page_preview' => true,
                'reply_markup' => json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => "👀 مشاهده حریف", 'callback_data' => "view_rival"],
                            ['text' => "❌ پایان بازی", 'callback_data' => "end_game"]
                        ],
                        [
                            ['text' => "🔙 بازگشت", 'callback_data' => "gamerandom"]
                        ],
                    ]
                ])
            ]);
        }
    } else {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ حریفی یافت نشد!",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    }
}
elseif($data=="close"){
// حذف پیام پروفایل کاربر
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);

// بررسی اینکه آیا کاربر در بازی است یا نه
if(!empty($cuser["userfild"]["rival"]) && $cuser["userfild"]["ingame"] == "on") {
    // کاربر در بازی است - فقط پیام حذف شود و منو ارسال نشود
    return;
}

// کاربر در بازی نیست - ارسال منوی اصلی
jijibot('sendmessage',[
    'chat_id'=>$chatid,
    'text'=>"سلام $firstname 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
                ['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
            ],
            [
                ['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
                ['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
            ],
            [
                ['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
            ],
            [
                ['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
            ],
            [
                ['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
            ],
            [
                ['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
            ],
        ]
    ])
]);

$cuser["userfild"]["step"]="none";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
elseif($data=="accept_rules_for_game"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ قوانین تایید شد!

⚠️ حالا برای شروع بازی ابتدا باید نام مستعار خود را تنظیم کنید.

لطفا نام مستعار خود را ارسال کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
            ],
        ]
    ])
]);

// تنظیم step برای دریافت نام مستعار
$cuser["userfild"]["step"] = "set_nickname_for_game";
// game_target قبلاً ذخیره شده است
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="accept_rules_for_gamerandom"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// جلوگیری از اجرای مکرر
$currentTime = time();
if(isset($cuser["userfild"]["last_rules_action"]) && ($currentTime - $cuser["userfild"]["last_rules_action"]) < 3) {
    return;
}
$cuser["userfild"]["last_rules_action"] = $currentTime;

// تایید قوانین
$cuser["userfild"]["rules_accepted"] = true;

// بررسی وجود نام مستعار
if(empty($cuser["userfild"]["nickname"])) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"✅ قوانین تایید شد!

⚠️ حالا برای شروع بازی ابتدا باید نام مستعار خود را تنظیم کنید.

لطفا نام مستعار خود را ارسال کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // تنظیم step برای دریافت نام مستعار
    $cuser["userfild"]["step"] = "set_nickname_for_gamerandom";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
} else {
    // کاربر نام مستعار دارد، مستقیم به بازی برود
    $cuser["userfild"]["step"] = "none";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);

    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"✅ قوانین تایید شد!

🎮 انتخاب تعداد بازیکن

چند نفره می‌خواهید بازی کنید؟",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"👤 انفرادی",'callback_data'=>"game_players_1"]
                ],
                [
                    ['text'=>"👥 4 نفره",'callback_data'=>"game_players_4"],
                    ['text'=>"👥 3 نفره",'callback_data'=>"game_players_3"]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
                ],
            ]
        ])
    ]);






}
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="game_type_girl"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// ذخیره نوع بازی انتخابی
$cuser["userfild"]["game_type"] = "girl";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// نمایش انتخاب تعداد نفرات
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🎮 انتخاب تعداد بازیکن

چند نفره می‌خواهید بازی کنید؟",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"👤 انفرادی",'callback_data'=>"game_players_1"]
            ],
            [
                ['text'=>"👥 4 نفره",'callback_data'=>"game_players_4"],
                ['text'=>"👥 3 نفره",'callback_data'=>"game_players_3"]
            ],
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
            ],
        ]
    ])
]);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="game_type_boy"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// ذخیره نوع بازی انتخابی
$cuser["userfild"]["game_type"] = "boy";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// نمایش انتخاب تعداد نفرات
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🎮 انتخاب تعداد بازیکن

چند نفره می‌خواهید بازی کنید؟",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"👤 انفرادی",'callback_data'=>"game_players_1"]
            ],
            [
                ['text'=>"👥 4 نفره",'callback_data'=>"game_players_4"],
                ['text'=>"👥 3 نفره",'callback_data'=>"game_players_3"]
            ],
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
            ],
        ]
    ])
]);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="game_players_1"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// بررسی اینکه آیا کاربر جنسیت خود را تعیین کرده است
$userGender = isset($cuser['userfild']['gender']) ? $cuser['userfild']['gender'] : '';
if (empty($userGender)) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"⚠️ برای شروع بازی انفرادی ابتدا باید جنسیت خود را تعیین کنید.

لطفا جنسیت خود را انتخاب کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"👦 پسر",'callback_data'=>"gender_male_for_game"],
                    ['text'=>"👧 دختر",'callback_data'=>"gender_female_for_game"]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
                ],
            ]
        ])
    ]);
    return;
}

// بررسی موجودی سکه کاربر (فقط برای کاربران بدون اشتراک)
if (!hasActiveSubscription($fromid)) {
    $userCoins = isset($cuser['userfild']['coins']) ? $cuser['userfild']['coins'] : 20;
    if ($userCoins < 2) {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ سکه کافی ندارید!

برای شروع بازی انفرادی نیاز به 2 سکه دارید.
موجودی فعلی شما: $userCoins سکه

می‌توانید از طریق دکمه‌های زیر سکه دریافت کنید:",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🎁 کد هدیه",'callback_data'=>"gift_code"],
                        ['text'=>"🪙 سکه روزانه",'callback_data'=>"daily_coin"]
                    ],
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
                    ],
                ]
            ])
        ]);
        return;
    }
}

// بررسی اینکه کاربر در حال حاضر در بازی نباشد
if($cuser["userfild"]["ingame"] == "on"){
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"🌟 شما یک بازی در حال انجام دارید ابتدا آن را پایان دهید",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"👀 مشاهده حریف",'callback_data'=>"view_rival"],
                    ['text'=>"❌ پایان بازی",'callback_data'=>"end_game"]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
                ],
            ]
        ])
    ]);
    return;
}

// شروع جستجوی بازیکن
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🔍 در حال جستجو برای یافتن بازیکن...

⏳ لطفا صبر کنید تا بازیکن مناسب پیدا شود.

🎮 شما به زودی با یک بازیکن آنلاین همتا خواهید شد!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_search"]
            ],
        ]
    ])
]);

// ذخیره message_id برای پاک کردن بعدی
$cuser["userfild"]["search_message_id"] = $messageid;
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// اضافه کردن کاربر به صف جستجو
addToSearchQueue($fromid);

}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="cancel_search"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// حذف کاربر از صف جستجو
removeFromSearchQueue($fromid);

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"❌ جستجو لغو شد

🔙 شما به منوی اصلی بازگشتید.

🎮 انتخاب تعداد بازیکن

چند نفره می‌خواهید بازی کنید؟",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"👤 انفرادی",'callback_data'=>"game_players_1"]
            ],
            [
                ['text'=>"👥 4 نفره",'callback_data'=>"game_players_4"],
                ['text'=>"👥 3 نفره",'callback_data'=>"game_players_3"]
            ],
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
            ],
        ]
    ])
]);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}


elseif($data=="game_players_4"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// بررسی اینکه کاربر در بازی نباشد
if(isset($cuser["userfild"]["ingame"]) && $cuser["userfild"]["ingame"] == "on") {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ شما در حال حاضر در بازی هستید!",
        'show_alert' => true
    ]);
    return;
}

// بررسی نام مستعار
if(empty($cuser["userfild"]["nickname"])) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"⚠️ برای شروع بازی 4 نفره نیاز به نام مستعار دارید.

لطفا نام مستعار خود را ارسال کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // تنظیم step برای دریافت نام مستعار
    $cuser["userfild"]["step"] = "set_nickname_for_four_player";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    return;
}

// بررسی موجودی سکه کاربر (فقط برای کاربران بدون اشتراک)
if (!hasActiveSubscription($fromid)) {
    $userCoins = isset($cuser['userfild']['coins']) ? $cuser['userfild']['coins'] : 20;
    if ($userCoins < 2) {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ سکه کافی ندارید!

برای شروع بازی 4 نفره نیاز به 2 سکه دارید.
موجودی فعلی شما: $userCoins سکه

💡 راه‌های دریافت سکه:
• دریافت سکه روزانه رایگان
• خرید اشتراک ویژه
• استفاده از کدهای هدیه",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🪙 دریافت سکه",'callback_data'=>"coins"]
                    ],
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }
}

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🎉 شروع بازی 4 نفره!

🔍 در حال جستجو برای یافتن 3 بازیکن دیگر...
⏳ لطفا صبر کنید...

💡 در بازی 4 نفره جنسیت مهم نیست و بازیکنان به صورت رندوم انتخاب می‌شوند.",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_four_player_search"]
            ],
        ]
    ])
]);

// اضافه کردن کاربر به صف بازی 4 نفره
addToFourPlayerQueue($fromid);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}

elseif($data=="game_players_3"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// بررسی اینکه کاربر در بازی نباشد
if(isset($cuser["userfild"]["ingame"]) && $cuser["userfild"]["ingame"] == "on") {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ شما در حال حاضر در بازی هستید!",
        'show_alert' => true
    ]);
    return;
}

// بررسی نام مستعار
if(empty($cuser["userfild"]["nickname"])) {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"⚠️ برای شروع بازی 3 نفره نیاز به نام مستعار دارید.

لطفا نام مستعار خود را ارسال کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // تنظیم step برای دریافت نام مستعار
    $cuser["userfild"]["step"] = "set_nickname_for_three_player";
    $cuser = json_encode($cuser, true);
    file_put_contents("data/user/$fromid.json", $cuser);
    return;
}

// بررسی موجودی سکه کاربر (فقط برای کاربران بدون اشتراک)
if (!hasActiveSubscription($fromid)) {
    $userCoins = isset($cuser['userfild']['coins']) ? $cuser['userfild']['coins'] : 20;
    if ($userCoins < 2) {
        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"❌ سکه کافی ندارید!

برای شروع بازی 3 نفره نیاز به 2 سکه دارید.
موجودی فعلی شما: $userCoins سکه

💡 راه‌های دریافت سکه:
• دریافت سکه روزانه رایگان
• خرید اشتراک ویژه
• استفاده از کدهای هدیه",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🪙 دریافت سکه",'callback_data'=>"coins"]
                    ],
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        return;
    }
}

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🎉 شروع بازی 3 نفره!

🔍 در حال جستجو برای یافتن 2 بازیکن دیگر...
⏳ لطفا صبر کنید...

💡 در بازی 3 نفره جنسیت مهم نیست و بازیکنان به صورت رندوم انتخاب می‌شوند.",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_three_player_search"]
            ],
        ]
    ])
]);

// اضافه کردن کاربر به صف بازی 3 نفره
addToThreePlayerQueue($fromid);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}

elseif($data=="accept_rules_for_gamebylink"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ قوانین تایید شد!

⚠️ حالا برای ایجاد لینک بازی ابتدا باید نام مستعار خود را تنظیم کنید.

لطفا نام مستعار خود را ارسال کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
            ],
        ]
    ])
]);

// تنظیم step برای دریافت نام مستعار
$cuser["userfild"]["step"] = "set_nickname_for_gamebylink";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);
}
else
{
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid
]);
force_join($chatid, $firstname, $usernamebot);
}
}
elseif($data=="main_menu"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"سلام $firstname 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
                ['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
            ],
            [
                ['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
                ['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
            ],
            [
                ['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
            ],
            [
                ['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
            ],
            [
                ['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
            ],
            [
                ['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
            ],
        ]
    ])
]);

$cuser["userfild"]["step"]="none";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="cancel"){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"سلام $firstname 👋

به ربات چالشی جرعت و حقیقت خوش آمدید!

با استفاده از این ربات شما و دوستانتان می توانید تا ساعت ها با هم  سرگرم شوید.

<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور  /help مطالعه کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🍾 بازی در گروه",'callback_data'=>"group_game"],
	['text'=>"🎮 شروع بازی",'callback_data'=>"gamerandom"]
	],
	[
	['text'=>"🎮 بازی دوستانه",'callback_data'=>"gamebylink"],
	['text'=>"☎️ پشتیبانی",'callback_data'=>"support"]
	],
			[
	['text'=>"🎗 ثبت جرعت و حقیقت",'callback_data'=>"sup"],['text'=>"📚 راهنما",'callback_data'=>"help"]
	],
			[
	['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"💳 اشتراک",'callback_data'=>"subscription"]
	],
		[
	['text'=>"🏆 برترین ها",'callback_data'=>"top_users"],['text'=>"🔍 جستجو",'callback_data'=>"search_user"]
	],
		[
	['text'=>"👥 زیرمجموعه گیری",'callback_data'=>"referral_system"]
	],
              ]
        ])
	  	]);
unset($rival["user"]);
$rival = json_encode($rival,true);
file_put_contents("data/rival.json",$rival);
}
elseif($data=="group_game"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"✨ جرعت و حقیقت در گروه

کاربر گرامی شما می توانید با افزودن ربات به گروه و ادمین کردن آن بازی را در گروه با دستور /game شروع کنید.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"➕ افزودن به گروه",'url'=>"https://telegram.me/$usernamebot?startgroup=playgame"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="help"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"📚 راهنما


✱ راهنمای اجرای بازی :
1- ربات را به گروه خود اضافه کنید.
2- ربات را در گروه خود جهت شناسایی ادمین های گروه ادمین کنید.
3- می توانید در هر زمان با دستور game/ یک بازی را در گروه استارت کنید.
4- تنظیمات در گروه با دستور settings/ ارسال می شود.

✱ راهنمای نحوه بازی :
<blockquote>روش بازی به این شکل هست که بازیکن‌ها به شکل دایره وار بر روی زمین می‌نشینند و ۲ تا ظرف که روی یکی نوشته شده حقیقت و روی دیگری نوشته شده جرعت وجود دارد.در ظرف حقیقت سوالهایی نوشته شده که بازیکن‌ها باید به درستی به آنها جواب بدهند و در ظرف جرعت هم درخواستهایی هست که باز باید جسارت انجام آنها را داشته باشند.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
	  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="support"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"☎️ پشتیبانی ربات

پیام خود را ارسال کنید، پیام شما مستقیم به تیم پشتیبانی ارسال خواهد شد و در اسرع وقت پاسخ داده خواهد شد.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="support";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="subscription"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// دریافت اطلاعات اشتراک کاربر
$subscription_info = getSubscriptionInfo($cuser);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"💳 اشتراک ویژه

<blockquote>نوع اشتراک شما : " . $subscription_info['type'] . "
مدت زمان باقی مانده : " . $subscription_info['remaining'] . "</blockquote>

<blockquote>جهت خرید اشتراک یکی از پلن ها را انتخاب کنید:</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"💳 اشتراک 30 روزه : 21هزارتومان",'callback_data'=>"sub_30"]
	],
		[
	['text'=>"💳 اشتراک 60 روزه : 42هزارتومان",'callback_data'=>"sub_60"]
	],
		[
	['text'=>"💳 اشتراک 90 روزه : 63هزارتومان",'callback_data'=>"sub_90"]
	],
		[
	['text'=>"💳 اشتراک 1 ساله : 252هزارتومان",'callback_data'=>"sub_365"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"],['text'=>"✨ قابلیت ها",'callback_data'=>"features"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="features"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"✨ قابلیت ها

<blockquote>1. آزاد شدن چت در بازی ها
امکان ارسال پیام و چت کردن با سایر بازیکنان در طول بازی</blockquote>

<blockquote>2. شروع بازی بدون محدودیت سکه
دیگر نیازی به انتظار برای جمع آوری سکه ندارید.</blockquote>

<blockquote>3. نمایش استیکر VIP در کنار اسم
نشان ویژه VIP در کنار نام شما نمایش داده می شود.</blockquote>

<blockquote>4. دسترسی به سوالات بیشتر
مجموعه گسترده ای از سوالات جدید و متنوع در اختیار شما</blockquote>

<blockquote>5. دسته بندی جدید سوالات
سوالات بر اساس موضوعات مختلف دسته بندی شده اند.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="sub_30" || $data=="sub_60" || $data=="sub_90" || $data=="sub_365"){
if(check_membership($fromid, $channel1, $channel2)){
// تعیین نوع پلن و قیمت
$plan_info = array(
    "sub_30" => array("days" => 30, "price" => "21هزارتومان"),
    "sub_60" => array("days" => 60, "price" => "42هزارتومان"),
    "sub_90" => array("days" => 90, "price" => "63هزارتومان"),
    "sub_365" => array("days" => 365, "price" => "252هزارتومان")
);

$selected_plan = $plan_info[$data];
Session::set($fromid, 'selected_plan', $data);
Session::set($fromid, 'plan_days', $selected_plan['days']);
Session::set($fromid, 'plan_price', $selected_plan['price']);

// بررسی اینکه آیا کاربر قبلاً شماره تلفن تایید شده دارد یا نه
if(!empty($cuser["userfild"]["phone"])) {
    // کاربر قبلاً شماره تایید کرده - ایجاد لینک پرداخت
    $price = getPlanPrice($data);
    $amount = convertToRials($price);
    $paymentLink = generatePaymentLink($fromid, $amount, $data);

    // تعیین متن بر اساس نوع پلن
    $planText = "";
    switch($data) {
        case 'sub_30':
            $planText = "💳 اشتراک 30روزه : 21,000 تومان\n\nبا خرید این اشتراک، به مدت 30 روز می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
        case 'sub_60':
            $planText = "💳 اشتراک 60روزه : 42,000 تومان\n\nبا خرید این اشتراک، به مدت 60 روز می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
        case 'sub_90':
            $planText = "💳 اشتراک 90روزه : 63,000 تومان\n\nبا خرید این اشتراک، به مدت 90 روز می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
        case 'sub_365':
            $planText = "💳 اشتراک 1 ساله : 252,000 تومان\n\nبا خرید این اشتراک، به مدت 1 سال می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
    }

    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>$planText."\n\n<b>⚠️ برای پرداخت اتصال VPN خود را قطع نمائید.</b>",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"💳 پرداخت",'url'=>$paymentLink]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"subscription"]
                ]
            ]
        ])
    ]);
} else {
    // کاربر هنوز شماره تایید نکرده - درخواست تایید شماره
    jijibot('editmessagetext',[
                    'chat_id'=>$chatid,
         'message_id'=>$messageid,
    	'text'=>"☎️ تایید شماره تلفن

جهت ادامه فرآیند خرید لطفا شماره تلفن خود را با فرمت 09xxxxxxxxx ارسال کنید.

<blockquote>اخذ شماره تلفن به علت جلوگیری از عملیات های کلاهبرداری و فیشینگ است.</blockquote>",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
    		[
    	['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
    	],
                  ]
            ])
      	]);
    $cuser["userfild"]["step"]="phone_verification";
    $cuser = json_encode($cuser,true);
    file_put_contents("data/user/$fromid.json",$cuser);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="user_settings"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

$baseNickname = !empty($cuser["userfild"]["nickname"]) ? $cuser["userfild"]["nickname"] : "تنظیم نشده";
$displayNickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⚙️ تنظیمات کاربری

✱ نام مستعار : $displayNickname
✱ آیدی عددی : $fromid
✱ یوزرنیم : @$usernameca
✱ تاریخ ورود : ".(!empty($cuser["userfild"]["join_date"]) ? $cuser["userfild"]["join_date"] : "تنظیم نشده")."
✱ جنسیت : ".(!empty($cuser["userfild"]["gender"]) ? $cuser["userfild"]["gender"] : "تنظیم نشده")."
✱ شهر : ".(!empty($cuser["userfild"]["city"]) ? $cuser["userfild"]["city"] : "تنظیم نشده")."
✱ سن : ".(!empty($cuser["userfild"]["age"]) ? $cuser["userfild"]["age"]." سال" : "تنظیم نشده")."
✱ امتیاز : ".(isset($cuser["userfild"]["score"]) ? $cuser["userfild"]["score"] : "5")."
✱ سکه : ".(isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : "20")."
✱ توکن کاربری : <a href=\"https://t.me/jdarebot?start=".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."\">".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."</a>
✱ حریم خصوصی : غیرفعال

<blockquote>جهت مدیریت سایر موارد در حساب خود می توانید از دکمه های زیر استفاده کنید.</blockquote>",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔑 توکن اختصاصی",'callback_data'=>"custom_token"]
	],
		[
	['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"],['text'=>"🚫 مسدودها",'callback_data'=>"blocked_users"]
	],
		[
	['text'=>"🔒 حریم خصوصی",'callback_data'=>"privacy"],['text'=>"🪙 سکه",'callback_data'=>"coins"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="custom_token"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

if($isVIP) {
    jijibot('editmessagetext',[
                    'chat_id'=>$chatid,
         'message_id'=>$messageid,
    	'text'=>"🔑 توکن اختصاصی

    شما می‌توانید توکن اختصاصی خود را تنظیم کنید.

    📝 شرایط توکن اختصاصی:
    • حداقل 5 کاراکتر و حداکثر 10 کاراکتر
    • فقط حروف انگلیسی و اعداد مجاز است
    • نباید قبلاً توسط کاربر دیگری استفاده شده باشد
    • مثال: MyToken123

    ✍️ توکن اختصاصی مورد نظر خود را ارسال کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
    		[
    	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
    	],
                  ]
            ])
      	]);
    $cuser["userfild"]["step"]="set_custom_token";
    $cuser = json_encode($cuser,true);
    file_put_contents("data/user/$fromid.json",$cuser);
} else {
    // کاربر VIP نیست
    jijibot('editmessagetext',[
                    'chat_id'=>$chatid,
         'message_id'=>$messageid,
    	'text'=>"🔑 توکن اختصاصی

❌ این قابلیت فقط برای کاربران VIP در دسترس است.

✱ با خرید اشتراک VIP می‌توانید:
✱ تنظیم توکن اختصاصی
✱ و قابلیت های متنوع دیگر...

💳 برای خرید اشتراک از دکمه زیر استفاده کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
    		[
    	['text'=>"💳 خرید اشتراک",'callback_data'=>"subscription"]
    	],
    		[
    	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
    	],
                  ]
            ])
      	]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="search_user"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🔍 جستجوی کاربر

لطفا توکن کاربر مورد نظر را ارسال کنید.

📝 نکات:
• توکن باید بین 5 تا 10 کاراکتر باشد
• ترکیبی از حروف انگلیسی و اعداد
• مثال: Abc12 یا Abc123XY

✍️ حالا توکن کاربر را بنویسید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="search_user";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="referral_system"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// ساخت لینک زیرمجموعه گیری با ID عددی
$referralLink = "https://t.me/jdarebot?start=$fromid";

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"👥 سیستم زیرمجموعه گیری

🔗 لینک اختصاصی شما:
<code>$referralLink</code>

📚 راهنمای استفاده:
• این لینک را با دوستان خود به اشتراک بگذارید
• هر کسی که از طریق لینک شما وارد ربات شود، زیرمجموعه شما خواهد بود
• با دعوت هر دوست خود 10 سکه دریافت کنید",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"📤 اشتراک گذاری",'switch_inline_query'=>"@jDareBot invites you\n🎮 اولین ربات متفاوت جرعت و حقیقت تلگرام\n✱ از طریق لینک زیر وارد ربات شوید و بازی را شروع کنید:\n$referralLink"]
            ],
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="top_users"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// خواندن تمام فایل‌های کاربران و مرتب‌سازی بر اساس امتیاز
$userDir = "data/user/";
$users = [];

if (is_dir($userDir)) {
    $files = scandir($userDir);
    foreach ($files as $file) {
        if ($file != "." && $file != ".." && pathinfo($file, PATHINFO_EXTENSION) == "json") {
            $userId = pathinfo($file, PATHINFO_FILENAME);
            $userData = json_decode(file_get_contents($userDir . $file), true);

            if (isset($userData['userfild'])) {
                $score = isset($userData['userfild']['score']) ? intval($userData['userfild']['score']) : 5;
                $nickname = !empty($userData['userfild']['nickname']) ? $userData['userfild']['nickname'] : "ناشناس";

                // بررسی وضعیت VIP
                $isVIP = false;
                if (isset($userData['userfild']['subscription']) &&
                    isset($userData['userfild']['subscription']['is_active']) &&
                    $userData['userfild']['subscription']['is_active'] &&
                    isset($userData['userfild']['subscription']['end_date']) &&
                    $userData['userfild']['subscription']['end_date'] > time()) {
                    $isVIP = true;
                }

                $displayNickname = $isVIP ? "✨ " . $nickname : $nickname;
                $userToken = isset($userData['userfild']['user_token']) ? $userData['userfild']['user_token'] : "";

                $users[] = [
                    'user_id' => $userId,
                    'nickname' => $displayNickname,
                    'score' => $score,
                    'token' => $userToken
                ];
            }
        }
    }
}

// مرتب‌سازی کاربران بر اساس امتیاز (نزولی)
usort($users, function($a, $b) {
    return $b['score'] - $a['score'];
});

// ساخت متن برترین‌ها
$topUsersText = "🏆 برترین بازیکنان\n\n👑 رتبه‌بندی بر اساس امتیاز:\n\n";

// نمایش 10 کاربر برتر
$topCount = min(10, count($users));
$medals = ["🥇", "🥈", "🥉", "🏅", "🏅", "🏅", "🏅", "🏅", "🏅", "🏅"];

for ($i = 0; $i < $topCount; $i++) {
    $rank = $i + 1;
    $medal = $medals[$i];
    $userToken = $users[$i]['token'];
    $nickname = $users[$i]['nickname'];
    $score = $users[$i]['score'];

    // ایجاد لینک برای توکن
    if (!empty($userToken)) {
        $tokenLink = "<a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>";
        $topUsersText .= "$medal رتبه $rank: $nickname ($tokenLink) - $score امتیاز\n\n";
    } else {
        $topUsersText .= "$medal رتبه $rank: $nickname (نامشخص) - $score امتیاز\n\n";
    }
}

// یافتن رتبه کاربر فعلی
$currentUserRank = "نامشخص";
$currentUserScore = isset($cuser['userfild']['score']) ? intval($cuser['userfild']['score']) : 5;

for ($i = 0; $i < count($users); $i++) {
    if ($users[$i]['user_id'] == $fromid) {
        $currentUserRank = $i + 1;
        break;
    }
}

$topUsersText .= "✱ رتبه شما: $currentUserRank\n";
$topUsersText .= "✱ امتیاز شما: $currentUserScore\n\n";
$topUsersText .= "برای بهبود رتبه خود بیشتر بازی کنید!";

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>$topUsersText,
	'parse_mode'=>'HTML',
	'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="sup"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"📘 ثبت جرعت و حقیقت

کاربر گرامی جهت ثبت ابتدا از دکمه های زیر نوع ثبت را مشخص کنید از اینکه به تیم و ربات ما کمک می کنید صمیمانه سپاس گذاریم.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"💪 جرعت",'callback_data'=>"submit_dare"],['text'=>"💬 حقیقت",'callback_data'=>"submit_truth"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
    		]);
$cuser["userfild"]["step"]="sup";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="submit_dare"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"💪 ثبت جرعت

لطفا جرعت خود را به صورت متن ارسال کنید.

📝 نکات مهم:
• جرعت باید مناسب و قابل اجرا باشه
• از محتوای نامناسب خودداری کنید
• جرعت شما پس از بررسی به ربات اضافه می شود

✍️ حالا جرعت خود را بنویسید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"sup"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="submit_dare_text";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="submit_truth"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"💬 ثبت حقیقت

لطفا حقیقت خود را به صورت متن ارسال کنید.

📝 نکات مهم:
• سوال باید جالب و قابل پاسخ باشه
• از سوالات شخصی و نامناسب خودداری کنید
• حقیقت شما پس از بررسی به ربات اضافه می شود

✍️ حالا حقیقت خود را بنویسید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"sup"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="submit_truth_text";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="privacy"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🔒 حریم خصوصی

کاربر گرامی حفظ حریم خصوصی شما از اولویت های ما می باشد با فعال کردن این گزینه هیچ فردی نمی تواند پروفایل شما را مشاهده کند.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"✅ فعال",'callback_data'=>"privacy_on"],['text'=>"❌ غیرفعال",'callback_data'=>"privacy_off"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="privacy_on"){
if(check_membership($fromid, $channel1, $channel2)){
// فعال کردن حریم خصوصی
$cuser["userfild"]["privacy"] = "فعال";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);
$cuser = json_decode($cuser, true);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

$baseNickname = !empty($cuser["userfild"]["nickname"]) ? $cuser["userfild"]["nickname"] : "تنظیم نشده";
$displayNickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⚙️ تنظیمات کاربری

✱ نام مستعار : $displayNickname
✱ آیدی عددی : $fromid
✱ یوزرنیم : @$usernameca
✱ تاریخ ورود : ".(!empty($cuser["userfild"]["join_date"]) ? $cuser["userfild"]["join_date"] : "تنظیم نشده")."
✱ جنسیت : ".(!empty($cuser["userfild"]["gender"]) ? $cuser["userfild"]["gender"] : "تنظیم نشده")."
✱ شهر : ".(!empty($cuser["userfild"]["city"]) ? $cuser["userfild"]["city"] : "تنظیم نشده")."
✱ سن : ".(!empty($cuser["userfild"]["age"]) ? $cuser["userfild"]["age"]." سال" : "تنظیم نشده")."
✱ امتیاز : ".(isset($cuser["userfild"]["score"]) ? $cuser["userfild"]["score"] : "5")."
✱ سکه : ".(isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : "20")."
✱ توکن کاربری : <a href=\"https://t.me/jdarebot?start=".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."\">".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."</a>
✱ حریم خصوصی : فعال

<blockquote>جهت مدیریت سایر موارد در حساب خود می توانید از دکمه های زیر استفاده کنید.</blockquote>",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔑 توکن اختصاصی",'callback_data'=>"custom_token"]
	],
		[
	['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"],['text'=>"🚫 مسدودها",'callback_data'=>"blocked_users"]
	],
		[
	['text'=>"🔒 حریم خصوصی",'callback_data'=>"privacy"],['text'=>"🪙 سکه",'callback_data'=>"coins"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
 jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
        'show_alert' =>true
    ]);
}
}
elseif($data=="privacy_off"){
if(check_membership($fromid, $channel1, $channel2)){
// غیرفعال کردن حریم خصوصی
$cuser["userfild"]["privacy"] = "غیرفعال";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);
$cuser = json_decode($cuser, true);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

$baseNickname = !empty($cuser["userfild"]["nickname"]) ? $cuser["userfild"]["nickname"] : "تنظیم نشده";
$displayNickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⚙️ تنظیمات کاربری

✱ نام مستعار : $displayNickname
✱ آیدی عددی : $fromid
✱ یوزرنیم : @$usernameca
✱ تاریخ ورود : ".(!empty($cuser["userfild"]["join_date"]) ? $cuser["userfild"]["join_date"] : "تنظیم نشده")."
✱ جنسیت : ".(!empty($cuser["userfild"]["gender"]) ? $cuser["userfild"]["gender"] : "تنظیم نشده")."
✱ شهر : ".(!empty($cuser["userfild"]["city"]) ? $cuser["userfild"]["city"] : "تنظیم نشده")."
✱ سن : ".(!empty($cuser["userfild"]["age"]) ? $cuser["userfild"]["age"]." سال" : "تنظیم نشده")."
✱ امتیاز : ".(isset($cuser["userfild"]["score"]) ? $cuser["userfild"]["score"] : "5")."
✱ سکه : ".(isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : "20")."
✱ توکن کاربری : <a href=\"https://t.me/jdarebot?start=".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."\">".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."</a>
✱ حریم خصوصی : غیرفعال

<blockquote>جهت مدیریت سایر موارد در حساب خود می توانید از دکمه های زیر استفاده کنید.</blockquote>",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔑 توکن اختصاصی",'callback_data'=>"custom_token"]
	],
		[
	['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"],['text'=>"🚫 مسدودها",'callback_data'=>"blocked_users"]
	],
		[
	['text'=>"🔒 حریم خصوصی",'callback_data'=>"privacy"],['text'=>"🪙 سکه",'callback_data'=>"coins"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
 jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
        'show_alert' =>true
    ]);
}
}
elseif($data=="blocked_users"){
if(check_membership($fromid, $channel1, $channel2)){
// به‌روزرسانی آخرین بازدید
updateLastVisit($fromid);

// دریافت لیست کاربران مسدود شده
$blockedUsers = isset($cuser['userfild']['blocked_users']) ? $cuser['userfild']['blocked_users'] : [];

if(empty($blockedUsers)) {
    // هیچ کاربری مسدود نشده
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"🚫 مسدودها

شما هیچ کاربری را مسدود نکرده‌اید.

<blockquote>کاربران مسدود شده قادر به ارسال پیام یا دعوت بازی به شما نخواهند بود.</blockquote>",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
                ],
            ]
        ])
    ]);
} else {
    // نمایش لیست کاربران مسدود شده
    $blockedList = "🚫 مسدودها\n\nلیست کاربران مسدود شده:\n\n";
    $keyboard = [];

    foreach($blockedUsers as $index => $blockedUserId) {
        $blockedUserFile = "data/user/$blockedUserId.json";
        if(file_exists($blockedUserFile)) {
            $blockedUserData = json_decode(file_get_contents($blockedUserFile), true);
            $blockedNickname = !empty($blockedUserData['userfild']['nickname']) ? $blockedUserData['userfild']['nickname'] : "ناشناس";
            $blockedToken = isset($blockedUserData['userfild']['user_token']) ? $blockedUserData['userfild']['user_token'] : "نامشخص";

            $blockedList .= "👤 $blockedNickname (<a href=\"https://t.me/jdarebot?start=$blockedToken\">$blockedToken</a>)\n";

            // اضافه کردن دکمه رفع مسدودیت
            $keyboard[] = [['text' => "🔓 رفع مسدودیت $blockedNickname", 'callback_data' => "unblock_$blockedUserId"]];
        }
    }

    $blockedList .= "\n<blockquote>برای رفع مسدودیت هر کاربر، روی دکمه مربوطه کلیک کنید.</blockquote>";

    // اضافه کردن دکمه برگشت
    $keyboard[] = [['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]];

    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>$blockedList,
        'parse_mode'=>'HTML',
        'disable_web_page_preview'=>true,
        'reply_markup'=>json_encode([
            'inline_keyboard'=>$keyboard
        ])
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="coins"){
if(check_membership($fromid, $channel1, $channel2)){
// دریافت موجودی سکه کاربر (پیش‌فرض 20 سکه)
$user_coins = isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : 20;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🪙 سکه

✱ موجودی فعلی : $user_coins سکه

در این بخش می توانید سکه های حساب کاربری خود را مدیریت کنید.",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🎁 کد هدیه",'callback_data'=>"gift_code"],['text'=>"🪙 سکه روزانه",'callback_data'=>"daily_coin"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="daily_coin"){
if(check_membership($fromid, $channel1, $channel2)){
// بررسی آخرین دریافت سکه روزانه
$today = date('Y-m-d');
$last_daily = isset($cuser["userfild"]["last_daily_coin"]) ? $cuser["userfild"]["last_daily_coin"] : '';

if($last_daily == $today) {
    // امروز قبلاً سکه دریافت کرده
    jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "❌ شما امروز قبلاً سکه روزانه دریافت کرده‌اید!",
        'show_alert' =>true
    ]);
} else {
    // اعطای سکه روزانه (20 سکه)
    $current_coins = isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : 20;
    $new_coins = $current_coins + 20;

    // به‌روزرسانی اطلاعات کاربر
    $cuser["userfild"]["coins"] = $new_coins;
    $cuser["userfild"]["last_daily_coin"] = $today;
    $cuser = json_encode($cuser,true);
    file_put_contents("data/user/$fromid.json",$cuser);

    jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "✨ 20 سکه روزانه دریافت شد! موجودی جدید: $new_coins سکه",
        'show_alert' =>true
    ]);

    // بازگشت به صفحه سکه با موجودی جدید
    jijibot('editmessagetext',[
                    'chat_id'=>$chatid,
         'message_id'=>$messageid,
    	'text'=>"🪙 سکه

✱ موجودی فعلی : $new_coins سکه

در این بخش می توانید سکه های حساب کاربری خود را مدیریت کنید.",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
    		[
    	['text'=>"🎁 کد هدیه",'callback_data'=>"gift_code"],['text'=>"🪙 سکه روزانه",'callback_data'=>"daily_coin"]
    	],
    		[
    	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
    	],
                  ]
            ])
          	]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="gift_code"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🎁 کد هدیه

لطفا کد هدیه خود را ارسال کنید:

<blockquote>کدهای هدیه را می‌توانید از کانال‌های ما یا از طریق مسابقات دریافت کنید.</blockquote>",
'parse_mode'=>'HTML',
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"coins"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="gift_code_input";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "message_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("message_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد به خودش پیام ارسال کند
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
    return;
}

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // بررسی حریم خصوصی کاربر هدف
    $targetPrivacy = isset($targetUserData['userfild']['privacy']) ? $targetUserData['userfild']['privacy'] : 'غیرفعال';

    if($targetPrivacy == 'فعال' && $targetUserId != $fromid) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ این کاربر حریم خصوصی فعال دارد و نمی‌توانید به او پیام ارسال کنید.",
            'show_alert' => true
        ]);
    } elseif(is_user_blocked($fromid, $targetUserId)) {
        // بررسی مسدودیت
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "🚫 شما توسط این کاربر مسدود شده‌اید و نمی‌توانید پیام ارسال کنید.",
            'show_alert' => true
        ]);
    } else {
        // بررسی وجود نام مستعار کاربر فرستنده
        if(empty($cuser['userfild']['nickname'])) {
            jijibot('sendmessage',[
                'chat_id'=>$chatid,
                'text'=>"⚠️ برای ارسال پیام ابتدا باید نام مستعار تنظیم کنید

لطفا ابتدا از بخش تنظیمات کاربری، نام مستعار خود را تنظیم کنید و سپس مجدداً تلاش کنید.",
                'parse_mode'=>'HTML',
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"]
                        ],
                        [
                            ['text'=>"🔙 برگشت",'callback_data'=>"back"]
                        ],
                    ]
                ])
            ]);
        } else {
            jijibot('sendmessage',[
                'chat_id'=>$chatid,
                'text'=>"💬 ارسال پیام به $targetNickname

لطفا پیام خود را تایپ کنید:

<blockquote>پیام شما به کاربر ارسال خواهد شد.</blockquote>",
                'parse_mode'=>'HTML',
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 برگشت",'callback_data'=>"back"]
                        ],
                    ]
                ])
            ]);

            // ذخیره آیدی کاربر هدف در session برای ارسال پیام
            $cuser["userfild"]["step"] = "send_message";
            $cuser["userfild"]["message_target"] = $targetUserId;
            $cuser = json_encode($cuser,true);
            file_put_contents("data/user/$fromid.json",$cuser);
        }
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="register_info"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🗄 ثبت اطلاعات

کاربر گرامی جهت شروع بازی حتما می بایست ابتدا یک نام مستعار برای خود تنظیم کنید لازم به ذکر است تنظیم بقیه موارد اجباری نخواهد بود.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🗄 تصویر",'callback_data'=>"set_photo"]
	],
		[
	['text'=>"🗄 سن",'callback_data'=>"set_age"],['text'=>"🗄 نام مستعار",'callback_data'=>"set_nickname"]
	],
		[
	['text'=>"🗄 شهر",'callback_data'=>"set_city"],['text'=>"🗄 جنسیت",'callback_data'=>"set_gender"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="register_info"){
if(check_membership($from_id, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🗄 ثبت اطلاعات

کاربر گرامی جهت شروع بازی حتما می بایست ابتدا یک نام مستعار برای خود تنظیم کنید لازم به ذکر است تنظیم بقیه موارد اجباری نخواهد بود.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🗄 تصویر",'callback_data'=>"set_photo"]
	],
		[
	['text'=>"🗄 سن",'callback_data'=>"set_age"],['text'=>"🗄 نام مستعار",'callback_data'=>"set_nickname"]
	],
		[
	['text'=>"🗄 شهر",'callback_data'=>"set_city"],['text'=>"🗄 جنسیت",'callback_data'=>"set_gender"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="set_photo"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"📸 تنظیم تصویر پروفایل

لطفا تصویر مورد نظر خود را ارسال کنید.

📝 نکات مهم:
• تصویر باید در فرمت عکس باشد (JPG, PNG)
• حداکثر حجم: 20 مگابایت
• تصویر شما در پروفایل نمایش داده خواهد شد

✍️ حالا تصویر خود را ارسال کنید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="set_photo";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="set_nickname"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🗄 تنظیم نام مستعار

لطفا نام مستعار خود را ارسال کنید.

📝 نکات مهم:
• نام مستعار باید بین 3 تا 22 کاراکتر باشد
• از کاراکترهای مناسب استفاده کنید
• نام مستعار شما در بازی نمایش داده خواهد شد

🚫 مجاز نیست:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

✍️ حالا نام مستعار خود را بنویسید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="set_nickname";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif ($juser["userfild"]["step"] == "game") {
if($textmassage == true){
         jijibot('sendmessage',[
        	'chat_id'=>$juser["userfild"]["rival"],
        	'text'=>"💬 درخواست حریف از شما : $textmassage

✱ لطفا پاسخ را به صورت متن و یا سایر محتوا ارسال کنید.",
 ]);
			         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"✅ ارسال شد

کاربر گرامی درخواست شما به حریف ارسال شد منتظر بمانید.",
 ]);
$userrival = $juser["userfild"]["rival"];
$getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
$getrival["userfild"]["step"]="answergame";
$getrival = json_encode($getrival,true);
file_put_contents("data/user/$userrival.json",$getrival);
}
else
{
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"❌ خطا

در این مرحله تنها ارسال متن به عنوان سوال ممکن است.",
 ]);
}
}
elseif ($juser["userfild"]["step"] == "four_player_question") {
if($textmassage == true){
    // دریافت اطلاعات بازی
    $gameId = $juser["userfild"]["four_player_game_id"];
    $gameData = getFourPlayerGameData($gameId);

    if (!$gameData) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ اطلاعات بازی یافت نشد!",
        ]);
        return;
    }

    // بررسی اینکه کاربر سوال‌کننده است
    if ($from_id != $gameData['current_questioner']) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ شما سوال‌کننده نیستید!",
        ]);
        return;
    }

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $answererNickname = $playerNicknames[$gameData['current_answerer']];
    $questionerNickname = $playerNicknames[$gameData['current_questioner']];
    $observer1Nickname = $playerNicknames[$gameData['current_observer1']];
    $observer2Nickname = $playerNicknames[$gameData['current_observer2']];

    // ارسال سوال به پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id' => $gameData['current_answerer'],
        'text' => "❓ سوال از $questionerNickname:

$textmassage

⏳ لطفا پاسخ خود را ارسال کنید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // ارسال سوال به مشاهده‌گران
    jijibot('sendmessage',[
        'chat_id' => $gameData['current_observer1'],
        'text' => "❓ سوال از $questionerNickname:

$textmassage

⏳ منتظر پاسخ $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    jijibot('sendmessage',[
        'chat_id' => $gameData['current_observer2'],
        'text' => "❓ سوال از $questionerNickname:

$textmassage

⏳ منتظر پاسخ $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // تایید برای سوال‌کننده
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ سوال ارسال شد

سوال شما برای $answererNickname ارسال شد.
⏳ منتظر پاسخ او باشید...",
    ]);

    // به‌روزرسانی وضعیت بازی
    $gameData['game_state'] = 'waiting_for_answer';
    $gameData['current_question'] = $textmassage;
    updateFourPlayerGameData($gameId, $gameData);

    // تنظیم step برای پاسخ‌دهنده
    $answererData = json_decode(file_get_contents("data/user/{$gameData['current_answerer']}.json"), true);
    $answererData['userfild']['step'] = 'four_player_answer';
    $answererData['userfild']['four_player_game_id'] = $gameId;
    file_put_contents("data/user/{$gameData['current_answerer']}.json", json_encode($answererData, true));

    // پاک کردن step سوال‌کننده
    $juser["userfild"]["step"] = "none";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);
}
else
{
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا

در این مرحله تنها ارسال متن به عنوان سوال ممکن است.",
 ]);
}
}
elseif ($juser["userfild"]["step"] == "three_player_question") {
if($textmassage == true){
    // دریافت اطلاعات بازی
    $gameId = $juser["userfild"]["three_player_game_id"];
    $gameData = getThreePlayerGameData($gameId);

    if (!$gameData) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ اطلاعات بازی یافت نشد!",
        ]);
        return;
    }

    // بررسی اینکه کاربر سوال‌کننده است
    if ($from_id != $gameData['current_questioner']) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ شما سوال‌کننده نیستید!",
        ]);
        return;
    }

    // بررسی وضعیت بازی
    if ($gameData['game_state'] != 'waiting_for_question') {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ زمان پرسیدن سوال گذشته است!",
        ]);
        return;
    }

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $choiceText = $gameData['current_choice'] == "dare" ? "جرعت" : "حقیقت";
    $answererNickname = $playerNicknames[$gameData['current_answerer']];

    // ارسال سوال به پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id'=>$gameData['current_answerer'],
        'text'=>"💬 درخواست $choiceText از شما : $textmassage

✱ لطفا پاسخ را به صورت متن و یا سایر محتوا ارسال کنید.",
    ]);

    // ارسال سوال به مشاهده‌گر
    jijibot('sendmessage',[
        'chat_id'=>$gameData['current_observer'],
        'text'=>"💬 سوال $choiceText : $textmassage

👤 سوال‌کننده: " . $playerNicknames[$gameData['current_questioner']] . "
👤 پاسخ‌دهنده: $answererNickname

⏳ منتظر پاسخ $answererNickname باشید...",
    ]);

    // تایید برای سوال‌کننده
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ سوال ارسال شد

سوال شما برای $answererNickname ارسال شد.
⏳ منتظر پاسخ او باشید...",
    ]);

    // به‌روزرسانی وضعیت بازی
    $gameData['game_state'] = 'waiting_for_answer';
    $gameData['current_question'] = $textmassage;
    updateThreePlayerGameData($gameId, $gameData);

    // تنظیم step برای پاسخ‌دهنده
    $answererData = json_decode(file_get_contents("data/user/{$gameData['current_answerer']}.json"), true);
    $answererData['userfild']['step'] = 'three_player_answer';
    $answererData['userfild']['three_player_game_id'] = $gameId;
    file_put_contents("data/user/{$gameData['current_answerer']}.json", json_encode($answererData, true));

    // پاک کردن step سوال‌کننده
    $juser["userfild"]["step"] = "none";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);
}
else
{
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا

در این مرحله تنها ارسال متن به عنوان سوال ممکن است.",
    ]);
}
}
elseif ($juser["userfild"]["step"] == "answergame") {
$photo = $message->photo;
$filephoto = $photo[count($photo)-1]->file_id;
$voice = $message->voice;
$filevoice = $voice->file_id;
$document = $update->message->document;
$filedocument = $document->file_id;
$sticker = $update->message->sticker;
$filesticker = $sticker->file_id;
$caption = $update->message->caption;
$userrival = $juser["userfild"]["rival"];
         jijibot('sendmessage',[
        	'chat_id'=>$userrival,
        	'text'=>"$textmassage",
 ]);
 jijibot('sendphoto',[
	'chat_id'=>"$userrival",
	'photo'=>$filephoto,
	'caption'=>$caption,
    		]);
			jijibot('senddocument',[
	'chat_id'=>"$userrival",
	'document'=>$filedocument,
	'caption'=>$caption,
    		]);
			jijibot('sendsticker',[
	'chat_id'=>"$userrival",
		'sticker'=>"$filesticker",
    		]);
	jijibot('sendvoice',[
	'chat_id'=>$userrival,
	'voice'=>$filevoice,
		'caption'=>$caption,
    		]);
			         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"✅ ارسال شد

پاسخ شما برای حریف ارسال شد ادامه بازی...",
 ]);
         jijibot('sendmessage',[
        	'chat_id'=>$userrival,
        	'text'=>"🔥 پاسخ دریافت شد

کاربر گرامی در پیام بالا حریف پاسخ شما را ارسال کرده است.",
 ]);
			         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
	'text'=>"🎲 دور بعدی

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
	  'reply_markup'=>json_encode([
    'keyboard'=>[
		 	  	  	 [
					 ['text'=>"👀 مشاهده حریف"],
					 ['text'=>"❌ پایان بازی"]
		 ],
   ],
      'resize_keyboard'=>true
   ])
	  	]);
jijibot('sendmessage',[
	'chat_id'=>$userrival,
	'text'=>"🎲 دور بعدی

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
	  'reply_markup'=>json_encode([
    'keyboard'=>[
		 	  	  	 [
					 ['text'=>"👀 مشاهده حریف"],
					 ['text'=>"❌ پایان بازی"]
		 ],
   ],
      'resize_keyboard'=>true
   ])
    		]);
$array = array("$from_id","$userrival");
$random = array_rand($array);
jijibot('sendmessage',[
	'chat_id'=>$array[$random],
	'text'=>"✨ نوبت شما است که سوال بپرسید

لطفا منتظر بمانید تا حریف شما جرعت یا حقیقت را انتخاب کند.",
    		]);
$result = array_diff($array , array($array[$random]));
jijibot('sendmessage',[
	'chat_id'=>$result[0],
	'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
	],
              ]
        ])
    		]);
			jijibot('sendmessage',[
	'chat_id'=>$result[1],
	'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
			[
	['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
	],
              ]
        ])
    		]);
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);
$getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
$getrival["userfild"]["step"]="none";
$getrival = json_encode($getrival,true);
file_put_contents("data/user/$userrival.json",$getrival);
}
// پردازش پیام‌های مشاهده‌گران در بازی 4 نفره
elseif (isUserInFourPlayerGame($from_id) && $juser["userfild"]["step"] != "four_player_answer") {
    $gameId = getUserFourPlayerGameId($from_id);
    $gameData = getFourPlayerGameData($gameId);

    if ($gameData && (getUserRoleInFourPlayerGame($from_id, $gameId) == 'observer1' || getUserRoleInFourPlayerGame($from_id, $gameId) == 'observer2')) {
        // مشاهده‌گر پیام ارسال کرده - باید به سوال‌کننده فرستاده شود
        $questioner_id = $gameData['current_questioner'];

        // دریافت نام مستعار مشاهده‌گر
        $observerData = json_decode(file_get_contents("data/user/$from_id.json"), true);
        $observerNickname = !empty($observerData['userfild']['nickname']) ? $observerData['userfild']['nickname'] : "ناشناس";

        // تشخیص نوع مشاهده‌گر
        $observerRole = getUserRoleInFourPlayerGame($from_id, $gameId);
        $observerNumber = $observerRole == 'observer1' ? '1' : '2';

        // ارسال پیام به سوال‌کننده
        if ($textmassage == true) {
            jijibot('sendmessage',[
                'chat_id' => $questioner_id,
                'text' => "💬 پیام از مشاهده‌گر $observerNumber ($observerNickname):

$textmassage",
                'reply_markup' => json_encode([
                    'keyboard' => [
                        [
                            ['text' => "👥 مشاهده بازیکنان"],
                            ['text' => "❌ ترک بازی"]
                        ],
                    ],
                    'resize_keyboard' => true
                ])
            ]);

            // تایید برای مشاهده‌گر
            jijibot('sendmessage',[
                'chat_id' => $from_id,
                'text' => "✅ پیام شما به سوال‌کننده ارسال شد.",
            ]);
        } else {
            jijibot('sendmessage',[
                'chat_id' => $from_id,
                'text' => "❌ فقط ارسال متن امکان‌پذیر است.",
            ]);
        }

        return; // پایان پردازش برای مشاهده‌گر
    }
    elseif ($gameData && getUserRoleInFourPlayerGame($from_id, $gameId) == 'questioner') {
        // سوال‌کننده پیام ارسال کرده - باید به مشاهده‌گران فرستاده شود
        $observer1_id = $gameData['current_observer1'];
        $observer2_id = $gameData['current_observer2'];

        // دریافت نام مستعار سوال‌کننده
        $questionerData = json_decode(file_get_contents("data/user/$from_id.json"), true);
        $questionerNickname = !empty($questionerData['userfild']['nickname']) ? $questionerData['userfild']['nickname'] : "ناشناس";

        // ارسال پیام به هر دو مشاهده‌گر
        if ($textmassage == true) {
            jijibot('sendmessage',[
                'chat_id' => $observer1_id,
                'text' => "💬 پیام از سوال‌کننده ($questionerNickname):

$textmassage",
                'reply_markup' => json_encode([
                    'keyboard' => [
                        [
                            ['text' => "👥 مشاهده بازیکنان"],
                            ['text' => "❌ ترک بازی"]
                        ],
                    ],
                    'resize_keyboard' => true
                ])
            ]);

            jijibot('sendmessage',[
                'chat_id' => $observer2_id,
                'text' => "💬 پیام از سوال‌کننده ($questionerNickname):

$textmassage",
                'reply_markup' => json_encode([
                    'keyboard' => [
                        [
                            ['text' => "👥 مشاهده بازیکنان"],
                            ['text' => "❌ ترک بازی"]
                        ],
                    ],
                    'resize_keyboard' => true
                ])
            ]);

            // تایید برای سوال‌کننده
            jijibot('sendmessage',[
                'chat_id' => $from_id,
                'text' => "✅ پیام شما به مشاهده‌گران ارسال شد.",
            ]);
        } else {
            jijibot('sendmessage',[
                'chat_id' => $from_id,
                'text' => "❌ فقط ارسال متن امکان‌پذیر است.",
            ]);
        }

        return; // پایان پردازش برای سوال‌کننده
    }
}
// پردازش پیام‌های مشاهده‌گر در بازی 3 نفره
elseif (isUserInThreePlayerGame($from_id) && $juser["userfild"]["step"] != "three_player_answer") {
    $gameId = getUserThreePlayerGameId($from_id);
    $gameData = getThreePlayerGameData($gameId);

    if ($gameData && getUserRoleInThreePlayerGame($from_id, $gameId) == 'observer') {
        // مشاهده‌گر پیام ارسال کرده - باید به سوال‌کننده فرستاده شود
        $questioner_id = $gameData['current_questioner'];

        // دریافت نام مستعار مشاهده‌گر
        $observerData = json_decode(file_get_contents("data/user/$from_id.json"), true);
        $observerNickname = !empty($observerData['userfild']['nickname']) ? $observerData['userfild']['nickname'] : "ناشناس";

        // ارسال انواع محتوا به سوال‌کننده
        $photo = $message->photo;
        $filephoto = $photo[count($photo)-1]->file_id;
        $voice = $message->voice;
        $filevoice = $voice->file_id;
        $document = $update->message->document;
        $filedocument = $document->file_id;
        $sticker = $update->message->sticker;
        $filesticker = $sticker->file_id;
        $caption = $update->message->caption;

        // ارسال پیام متنی (با نام فرستنده)
        if($textmassage) {
            jijibot('sendmessage',[
                'chat_id'=>$questioner_id,
                'text'=>"💬 پیام از طرف مشاهده‌گر ($observerNickname): $textmassage",
            ]);
        }

        // ارسال عکس (با نام فرستنده)
        if($filephoto) {
            $photoCaption = "💬 پیام از طرف مشاهده‌گر ($observerNickname)";
            if($caption) {
                $photoCaption .= ": $caption";
            }
            jijibot('sendphoto',[
                'chat_id'=>$questioner_id,
                'photo'=>$filephoto,
                'caption'=>$photoCaption,
            ]);
        }

        // ارسال فایل (با نام فرستنده)
        if($filedocument) {
            $docCaption = "💬 پیام از طرف مشاهده‌گر ($observerNickname)";
            if($caption) {
                $docCaption .= ": $caption";
            }
            jijibot('senddocument',[
                'chat_id'=>$questioner_id,
                'document'=>$filedocument,
                'caption'=>$docCaption,
            ]);
        }

        // ارسال استیکر (با پیام جداگانه برای نام فرستنده)
        if($filesticker) {
            jijibot('sendmessage',[
                'chat_id'=>$questioner_id,
                'text'=>"💬 استیکر از طرف مشاهده‌گر ($observerNickname):",
            ]);
            jijibot('sendsticker',[
                'chat_id'=>$questioner_id,
                'sticker'=>$filesticker,
            ]);
        }

        // ارسال ویس (با نام فرستنده)
        if($filevoice) {
            $voiceCaption = "💬 پیام صوتی از طرف مشاهده‌گر ($observerNickname)";
            if($caption) {
                $voiceCaption .= ": $caption";
            }
            jijibot('sendvoice',[
                'chat_id'=>$questioner_id,
                'voice'=>$filevoice,
                'caption'=>$voiceCaption,
            ]);
        }

        // تایید برای مشاهده‌گر
        jijibot('sendmessage',[
            'chat_id'=>$from_id,
            'text'=>"✅ پیام شما برای سوال‌کننده ارسال شد",
        ]);

        return; // پایان پردازش برای مشاهده‌گر
    }

    // پردازش پیام‌های سوال‌کننده به مشاهده‌گر (فقط تا قبل از انتخاب جرعت/حقیقت)
    elseif ($gameData && getUserRoleInThreePlayerGame($from_id, $gameId) == 'questioner' &&
            $gameData['game_state'] == 'waiting_for_choice') {

        $observer_id = $gameData['current_observer'];

        // دریافت نام مستعار سوال‌کننده
        $questionerData = json_decode(file_get_contents("data/user/$from_id.json"), true);
        $questionerNickname = !empty($questionerData['userfild']['nickname']) ? $questionerData['userfild']['nickname'] : "ناشناس";

        // ارسال انواع محتوا به مشاهده‌گر
        $photo = $message->photo;
        $filephoto = $photo[count($photo)-1]->file_id;
        $voice = $message->voice;
        $filevoice = $voice->file_id;
        $document = $update->message->document;
        $filedocument = $document->file_id;
        $sticker = $update->message->sticker;
        $filesticker = $sticker->file_id;
        $caption = $update->message->caption;

        // ارسال پیام متنی (با نام فرستنده)
        if($textmassage) {
            jijibot('sendmessage',[
                'chat_id'=>$observer_id,
                'text'=>"💬 پیام از طرف سوال‌کننده ($questionerNickname): $textmassage",
            ]);
        }

        // ارسال عکس (با نام فرستنده)
        if($filephoto) {
            $photoCaption = "💬 پیام از طرف سوال‌کننده ($questionerNickname)";
            if($caption) {
                $photoCaption .= ": $caption";
            }
            jijibot('sendphoto',[
                'chat_id'=>$observer_id,
                'photo'=>$filephoto,
                'caption'=>$photoCaption,
            ]);
        }

        // ارسال فایل (با نام فرستنده)
        if($filedocument) {
            $docCaption = "💬 پیام از طرف سوال‌کننده ($questionerNickname)";
            if($caption) {
                $docCaption .= ": $caption";
            }
            jijibot('senddocument',[
                'chat_id'=>$observer_id,
                'document'=>$filedocument,
                'caption'=>$docCaption,
            ]);
        }

        // ارسال استیکر (با پیام جداگانه برای نام فرستنده)
        if($filesticker) {
            jijibot('sendmessage',[
                'chat_id'=>$observer_id,
                'text'=>"💬 استیکر از طرف سوال‌کننده ($questionerNickname):",
            ]);
            jijibot('sendsticker',[
                'chat_id'=>$observer_id,
                'sticker'=>$filesticker,
            ]);
        }

        // ارسال ویس (با نام فرستنده)
        if($filevoice) {
            $voiceCaption = "💬 پیام صوتی از طرف سوال‌کننده ($questionerNickname)";
            if($caption) {
                $voiceCaption .= ": $caption";
            }
            jijibot('sendvoice',[
                'chat_id'=>$observer_id,
                'voice'=>$filevoice,
                'caption'=>$voiceCaption,
            ]);
        }

        // تایید برای سوال‌کننده
        jijibot('sendmessage',[
            'chat_id'=>$from_id,
            'text'=>"✅ پیام شما برای مشاهده‌گر ارسال شد",
        ]);

        return; // پایان پردازش برای سوال‌کننده
    }
}

// ادامه پردازش پاسخ‌دهنده در بازی 4 نفره
elseif ($juser["userfild"]["step"] == "four_player_answer") {
    // دریافت اطلاعات بازی
    $gameId = $juser["userfild"]["four_player_game_id"];
    $gameData = getFourPlayerGameData($gameId);

    if (!$gameData) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ اطلاعات بازی یافت نشد!",
        ]);
        return;
    }

    // بررسی اینکه کاربر پاسخ‌دهنده است
    if ($from_id != $gameData['current_answerer']) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ شما پاسخ‌دهنده نیستید!",
        ]);
        return;
    }

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $answererNickname = $playerNicknames[$gameData['current_answerer']];
    $questionerNickname = $playerNicknames[$gameData['current_questioner']];
    $observer1Nickname = $playerNicknames[$gameData['current_observer1']];
    $observer2Nickname = $playerNicknames[$gameData['current_observer2']];

    // ارسال پاسخ به همه بازیکنان
    foreach ($gameData['players'] as $playerId) {
        if ($playerId == $from_id) {
            // پیام تایید برای پاسخ‌دهنده
            jijibot('sendmessage',[
                'chat_id' => $playerId,
                'text' => "✅ پاسخ شما ارسال شد!

💬 پاسخ شما: $textmassage

🎲 آماده‌سازی دور بعدی...",
                'reply_markup' => json_encode([
                    'keyboard' => [
                        [
                            ['text' => "👥 مشاهده بازیکنان"],
                            ['text' => "❌ ترک بازی"]
                        ],
                    ],
                    'resize_keyboard' => true
                ])
            ]);
        } else {
            // پیام برای سایر بازیکنان
            jijibot('sendmessage',[
                'chat_id' => $playerId,
                'text' => "💬 پاسخ $answererNickname:

$textmassage

🎲 آماده‌سازی دور بعدی...",
                'reply_markup' => json_encode([
                    'keyboard' => [
                        [
                            ['text' => "👥 مشاهده بازیکنان"],
                            ['text' => "❌ ترک بازی"]
                        ],
                    ],
                    'resize_keyboard' => true
                ])
            ]);
        }
    }

    // پاک کردن step پاسخ‌دهنده
    $juser["userfild"]["step"] = "none";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);

    // شروع دور جدید
    startNewFourPlayerRound($gameId);
}

// ادامه پردازش پاسخ‌دهنده در بازی 3 نفره
elseif ($juser["userfild"]["step"] == "three_player_answer") {
    // دریافت اطلاعات بازی
    $gameId = $juser["userfild"]["three_player_game_id"];
    $gameData = getThreePlayerGameData($gameId);

    if (!$gameData) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ اطلاعات بازی یافت نشد!",
        ]);
        return;
    }

    // بررسی اینکه کاربر پاسخ‌دهنده است
    if ($from_id != $gameData['current_answerer']) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ شما پاسخ‌دهنده نیستید!",
        ]);
        return;
    }

    // بررسی وضعیت بازی
    if ($gameData['game_state'] != 'waiting_for_answer') {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ زمان پاسخ دادن گذشته است!",
        ]);
        return;
    }

    // دریافت انواع محتوا
    $photo = $message->photo;
    $filephoto = $photo[count($photo)-1]->file_id;
    $voice = $message->voice;
    $filevoice = $voice->file_id;
    $document = $update->message->document;
    $filedocument = $document->file_id;
    $sticker = $update->message->sticker;
    $filesticker = $sticker->file_id;
    $caption = $update->message->caption;

    // دریافت اطلاعات بازیکنان
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $answererNickname = $playerNicknames[$gameData['current_answerer']];

    // ارسال پاسخ به سوال‌کننده
    if($textmassage) {
        jijibot('sendmessage',[
            'chat_id'=>$gameData['current_questioner'],
            'text'=>"$textmassage",
        ]);
    }
    if($filephoto) {
        jijibot('sendphoto',[
            'chat_id'=>$gameData['current_questioner'],
            'photo'=>$filephoto,
            'caption'=>$caption,
        ]);
    }
    if($filedocument) {
        jijibot('senddocument',[
            'chat_id'=>$gameData['current_questioner'],
            'document'=>$filedocument,
            'caption'=>$caption,
        ]);
    }
    if($filesticker) {
        jijibot('sendsticker',[
            'chat_id'=>$gameData['current_questioner'],
            'sticker'=>$filesticker,
        ]);
    }
    if($filevoice) {
        jijibot('sendvoice',[
            'chat_id'=>$gameData['current_questioner'],
            'voice'=>$filevoice,
            'caption'=>$caption,
        ]);
    }

    // ارسال پاسخ به مشاهده‌گر
    if($textmassage) {
        jijibot('sendmessage',[
            'chat_id'=>$gameData['current_observer'],
            'text'=>"$textmassage",
        ]);
    }
    if($filephoto) {
        jijibot('sendphoto',[
            'chat_id'=>$gameData['current_observer'],
            'photo'=>$filephoto,
            'caption'=>$caption,
        ]);
    }
    if($filedocument) {
        jijibot('senddocument',[
            'chat_id'=>$gameData['current_observer'],
            'document'=>$filedocument,
            'caption'=>$caption,
        ]);
    }
    if($filesticker) {
        jijibot('sendsticker',[
            'chat_id'=>$gameData['current_observer'],
            'sticker'=>$filesticker,
        ]);
    }
    if($filevoice) {
        jijibot('sendvoice',[
            'chat_id'=>$gameData['current_observer'],
            'voice'=>$filevoice,
            'caption'=>$caption,
        ]);
    }

    // تایید برای پاسخ‌دهنده
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ پاسخ ارسال شد

پاسخ شما برای همه بازیکنان ارسال شد.",
    ]);

    // اطلاع‌رسانی دریافت پاسخ
    jijibot('sendmessage',[
        'chat_id'=>$gameData['current_questioner'],
        'text'=>"🔥 پاسخ دریافت شد

$answererNickname در پیام بالا پاسخ شما را ارسال کرده است.",
    ]);

    jijibot('sendmessage',[
        'chat_id'=>$gameData['current_observer'],
        'text'=>"🔥 پاسخ دریافت شد

$answererNickname در پیام بالا پاسخ را ارسال کرده است.",
    ]);

    // پاک کردن step پاسخ‌دهنده
    $juser["userfild"]["step"] = "none";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);

    // شروع دور بعدی
    startNewThreePlayerRound($gameId);
}


elseif($data=="set_gender"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🙎‍♂️ جنسیت

کاربر گرامی جنسیت خود را برای ثبت در سیستم و حساب کاربری از بین دکمه ها مشخص کنید.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🙎‍♀️ دختر",'callback_data'=>"gender_female"],['text'=>"🙎‍♂️ پسر",'callback_data'=>"gender_male"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
	],
              ]
        ])
  	]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="gender_male"){
if(check_membership($fromid, $channel1, $channel2)){
// ذخیره جنسیت پسر در فایل کاربر
$cuser["userfild"]["gender"] = "پسر";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);

// بارگذاری مجدد اطلاعات کاربر از فایل
$cuser = json_decode(file_get_contents("data/user/$fromid.json"),true);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

$baseNickname = !empty($cuser["userfild"]["nickname"]) ? $cuser["userfild"]["nickname"] : "تنظیم نشده";
$displayNickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⚙️ تنظیمات کاربری

✱ نام مستعار : $displayNickname
✱ آیدی عددی : $fromid
✱ یوزرنیم : @$usernameca
✱ تاریخ ورود : ".(!empty($cuser["userfild"]["join_date"]) ? $cuser["userfild"]["join_date"] : "تنظیم نشده")."
✱ جنسیت : ".(!empty($cuser["userfild"]["gender"]) ? $cuser["userfild"]["gender"] : "تنظیم نشده")."
✱ شهر : ".(!empty($cuser["userfild"]["city"]) ? $cuser["userfild"]["city"] : "تنظیم نشده")."
✱ سن : ".(!empty($cuser["userfild"]["age"]) ? $cuser["userfild"]["age"]." سال" : "تنظیم نشده")."
✱ امتیاز : ".(isset($cuser["userfild"]["score"]) ? $cuser["userfild"]["score"] : "5")."
✱ سکه : ".(isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : "20")."
✱ توکن کاربری : <a href=\"https://t.me/jdarebot?start=".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."\">".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."</a>
✱ حریم خصوصی : ".(!empty($cuser["userfild"]["privacy"]) ? $cuser["userfild"]["privacy"] : "غیرفعال")."

<blockquote>جهت مدیریت سایر موارد در حساب خود می توانید از دکمه های زیر استفاده کنید.</blockquote>",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔑 توکن اختصاصی",'callback_data'=>"custom_token"]
	],
		[
	['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"],['text'=>"🚫 مسدودها",'callback_data'=>"blocked_users"]
	],
		[
	['text'=>"🔒 حریم خصوصی",'callback_data'=>"privacy"],['text'=>"🪙 سکه",'callback_data'=>"coins"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
 jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
        'show_alert' =>true
    ]);
}
}
elseif($data=="gender_female"){
if(check_membership($fromid, $channel1, $channel2)){
// ذخیره جنسیت دختر در فایل کاربر
$cuser["userfild"]["gender"] = "دختر";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);

// بارگذاری مجدد اطلاعات کاربر از فایل
$cuser = json_decode(file_get_contents("data/user/$fromid.json"),true);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

$baseNickname = !empty($cuser["userfild"]["nickname"]) ? $cuser["userfild"]["nickname"] : "تنظیم نشده";
$displayNickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⚙️ تنظیمات کاربری

✱ نام مستعار : $displayNickname
✱ آیدی عددی : $fromid
✱ یوزرنیم : @$usernameca
✱ تاریخ ورود : ".(!empty($cuser["userfild"]["join_date"]) ? $cuser["userfild"]["join_date"] : "تنظیم نشده")."
✱ جنسیت : ".(!empty($cuser["userfild"]["gender"]) ? $cuser["userfild"]["gender"] : "تنظیم نشده")."
✱ شهر : ".(!empty($cuser["userfild"]["city"]) ? $cuser["userfild"]["city"] : "تنظیم نشده")."
✱ سن : ".(!empty($cuser["userfild"]["age"]) ? $cuser["userfild"]["age"]." سال" : "تنظیم نشده")."
✱ امتیاز : ".(isset($cuser["userfild"]["score"]) ? $cuser["userfild"]["score"] : "5")."
✱ سکه : ".(isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : "20")."
✱ توکن کاربری : <a href=\"https://t.me/jdarebot?start=".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."\">".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."</a>
✱ حریم خصوصی : ".(!empty($cuser["userfild"]["privacy"]) ? $cuser["userfild"]["privacy"] : "غیرفعال")."

<blockquote>جهت مدیریت سایر موارد در حساب خود می توانید از دکمه های زیر استفاده کنید.</blockquote>",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔑 توکن اختصاصی",'callback_data'=>"custom_token"]
	],
		[
	['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"],['text'=>"🚫 مسدودها",'callback_data'=>"blocked_users"]
	],
		[
	['text'=>"🔒 حریم خصوصی",'callback_data'=>"privacy"],['text'=>"🪙 سکه",'callback_data'=>"coins"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
 jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
        'show_alert' =>true
    ]);
}
}
elseif($data=="gender_male_for_game"){
if(check_membership($fromid, $channel1, $channel2)){
// ذخیره جنسیت پسر در فایل کاربر
$cuser["userfild"]["gender"] = "پسر";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);

// بازگشت به بازی انفرادی
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ جنسیت شما ثبت شد!

🔍 در حال جستجو برای یافتن بازیکن...

⏳ لطفا صبر کنید تا بازیکن مناسب پیدا شود.

🎮 شما به زودی با یک بازیکن آنلاین همتا خواهید شد!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_search"]
            ],
        ]
    ])
]);

// ذخیره message_id برای پاک کردن بعدی
$cuser["userfild"]["search_message_id"] = $messageid;
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// اضافه کردن کاربر به صف جستجو
addToSearchQueue($fromid);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="gender_female_for_game"){
if(check_membership($fromid, $channel1, $channel2)){
// ذخیره جنسیت دختر در فایل کاربر
$cuser["userfild"]["gender"] = "دختر";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);

// بازگشت به بازی انفرادی
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ جنسیت شما ثبت شد!

🔍 در حال جستجو برای یافتن بازیکن...

⏳ لطفا صبر کنید تا بازیکن مناسب پیدا شود.

🎮 شما به زودی با یک بازیکن آنلاین همتا خواهید شد!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_search"]
            ],
        ]
    ])
]);

// ذخیره message_id برای پاک کردن بعدی
$cuser["userfild"]["search_message_id"] = $messageid;
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// اضافه کردن کاربر به صف جستجو
addToSearchQueue($fromid);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="set_age"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🎚 سن

کاربر گرامی سن خود را برای ثبت ارسال کنید لازم به ذکر است سن باید +18 باشد.

📝 نکات مهم:
• سن باید بین 18 تا 50 سال باشد
• فقط عدد ارسال کنید
• سن شما در سیستم ثبت خواهد شد

✍️ حالا سن خود را بنویسید:",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
	],
              ]
        ])
  	]);
$cuser["userfild"]["step"]="set_age";
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="set_city"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"🏙 شهر

کاربر گرامی جهت انتخاب شهر خود را از موارد زیر انتخاب کنید.",
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🏠 تهران",'callback_data'=>"city_تهران"],['text'=>"🏠 اصفهان",'callback_data'=>"city_اصفهان"]
	],
		[
	['text'=>"🏠 فارس",'callback_data'=>"city_فارس"],['text'=>"🏠 هرمزگان",'callback_data'=>"city_هرمزگان"]
	],
		[
	['text'=>"🏠 خوزستان",'callback_data'=>"city_خوزستان"],['text'=>"🏠 کرمان",'callback_data'=>"city_کرمان"]
	],
		[
	['text'=>"🏠 خراسان رضوی",'callback_data'=>"city_خراسان رضوی"],['text'=>"🏠 آذربایجان شرقی",'callback_data'=>"city_آذربایجان شرقی"]
	],
		[
	['text'=>"🏠 مازندران",'callback_data'=>"city_مازندران"],['text'=>"🏠 گیلان",'callback_data'=>"city_گیلان"]
	],
		[
	['text'=>"🏠 کرمانشاه",'callback_data'=>"city_کرمانشاه"],['text'=>"🏠 آذربایجان غربی",'callback_data'=>"city_آذربایجان غربی"]
	],
		[
	['text'=>"🏠 لرستان",'callback_data'=>"city_لرستان"],['text'=>"🏠 همدان",'callback_data'=>"city_همدان"]
	],
		[
	['text'=>"🏠 مرکزی",'callback_data'=>"city_مرکزی"],['text'=>"🏠 ایلام",'callback_data'=>"city_ایلام"]
	],
		[
	['text'=>"🏠 بوشهر",'callback_data'=>"city_بوشهر"],['text'=>"🏠 قزوین",'callback_data'=>"city_قزوین"]
	],
		[
	['text'=>"🏠 قم",'callback_data'=>"city_قم"],['text'=>"🏠 کردستان",'callback_data'=>"city_کردستان"]
	],
		[
	['text'=>"🏠 سمنان",'callback_data'=>"city_سمنان"],['text'=>"🏠 یزد",'callback_data'=>"city_یزد"]
	],
		[
	['text'=>"🏠 خراسان شمالی",'callback_data'=>"city_خراسان شمالی"],['text'=>"🏠 خراسان جنوبی",'callback_data'=>"city_خراسان جنوبی"]
	],
		[
	['text'=>"🏠 البرز",'callback_data'=>"city_البرز"],['text'=>"🏠 اردبیل",'callback_data'=>"city_اردبیل"]
	],
		[
	['text'=>"🏠 زنجان",'callback_data'=>"city_زنجان"],['text'=>"🏠 گلستان",'callback_data'=>"city_گلستان"]
	],
		[
	['text'=>"🏠 چهارمحال و بختیاری",'callback_data'=>"city_چهارمحال و بختیاری"],['text'=>"🏠 کهگیلویه و بویراحمد",'callback_data'=>"city_کهگیلویه و بویراحمد"]
	],
		[
	['text'=>"🏠 سیستان و بلوچستان",'callback_data'=>"city_سیستان و بلوچستان"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
	],
              ]
        ])
  	]);
}
else
{
	force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "city_") === 0){
if(check_membership($fromid, $channel1, $channel2)){
// استخراج نام شهر از callback_data
$city_name = str_replace("city_", "", $data);

// ذخیره شهر در فایل کاربر
$cuser["userfild"]["city"] = $city_name;
$cuser = json_encode($cuser,true);
file_put_contents("data/user/$fromid.json",$cuser);

// بارگذاری مجدد اطلاعات کاربر از فایل
$cuser = json_decode(file_get_contents("data/user/$fromid.json"),true);

// بررسی وضعیت VIP کاربر
$isVIP = false;
if (isset($cuser['userfild']['subscription']) &&
    isset($cuser['userfild']['subscription']['is_active']) &&
    $cuser['userfild']['subscription']['is_active'] &&
    isset($cuser['userfild']['subscription']['end_date']) &&
    $cuser['userfild']['subscription']['end_date'] > time()) {
    $isVIP = true;
}

$baseNickname = !empty($cuser["userfild"]["nickname"]) ? $cuser["userfild"]["nickname"] : "تنظیم نشده";
$displayNickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;

jijibot('editmessagetext',[
                'chat_id'=>$chatid,
     'message_id'=>$messageid,
	'text'=>"⚙️ تنظیمات کاربری

✱ نام مستعار : $displayNickname
✱ آیدی عددی : $fromid
✱ یوزرنیم : @$usernameca
✱ تاریخ ورود : ".(!empty($cuser["userfild"]["join_date"]) ? $cuser["userfild"]["join_date"] : "تنظیم نشده")."
✱ جنسیت : ".(!empty($cuser["userfild"]["gender"]) ? $cuser["userfild"]["gender"] : "تنظیم نشده")."
✱ شهر : ".(!empty($cuser["userfild"]["city"]) ? $cuser["userfild"]["city"] : "تنظیم نشده")."
✱ سن : ".(!empty($cuser["userfild"]["age"]) ? $cuser["userfild"]["age"]." سال" : "تنظیم نشده")."
✱ امتیاز : ".(isset($cuser["userfild"]["score"]) ? $cuser["userfild"]["score"] : "5")."
✱ سکه : ".(isset($cuser["userfild"]["coins"]) ? $cuser["userfild"]["coins"] : "20")."
✱ توکن کاربری : <a href=\"https://t.me/jdarebot?start=".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."\">".(isset($cuser["userfild"]["user_token"]) ? $cuser["userfild"]["user_token"] : "نامشخص")."</a>
✱ حریم خصوصی : ".(!empty($cuser["userfild"]["privacy"]) ? $cuser["userfild"]["privacy"] : "غیرفعال")."

<blockquote>جهت مدیریت سایر موارد در حساب خود می توانید از دکمه های زیر استفاده کنید.</blockquote>",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'inline_keyboard'=>[
		[
	['text'=>"🔑 توکن اختصاصی",'callback_data'=>"custom_token"]
	],
		[
	['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"],['text'=>"🚫 مسدودها",'callback_data'=>"blocked_users"]
	],
		[
	['text'=>"🔒 حریم خصوصی",'callback_data'=>"privacy"],['text'=>"🪙 سکه",'callback_data'=>"coins"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
  	]);
}
else
{
 jijibot('answercallbackquery', [
        'callback_query_id' =>$membercall,
        'text' => "📍 برای استفاده از ربات باید در کانال @$channel عضو باشید",
        'show_alert' =>true
    ]);
}
}
elseif ($juser["userfild"]["step"] == "set_photo") {
// بررسی اینکه پیام شامل عکس است
if(isset($message->photo) && !empty($message->photo)){
    // دریافت بزرگترین سایز عکس
    $photo = end($message->photo);
    $file_id = $photo->file_id;

    // ایجاد پوشه تصاویر در صورت عدم وجود
    if (!file_exists("data/photos")) {
        mkdir("data/photos", 0777, true);
    }

    // ذخیره file_id تصویر در فایل کاربر
    $juser["userfild"]["photo"] = $file_id;
    $juser["userfild"]["step"] = "none";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);

    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ تصویر شما با موفقیت ثبت شد!

📸 تصویر پروفایل شما به‌روزرسانی شد.

می‌توانید از طریق دکمه زیر به تنظیمات کاربری برگردید.",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در ارسال تصویر

لطفا یک تصویر معتبر ارسال کنید.

📝 نکات:
• فقط فرمت‌های JPG و PNG پذیرفته می‌شود
• حداکثر حجم: 20 مگابایت
• تصویر باید واضح و مناسب باشد

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == "set_nickname") {
if($textmassage == true){
$nickname_length = mb_strlen($textmassage, 'UTF-8');

// بررسی وجود یوزرنیم، لینک و کاراکترهای غیرمجاز
$has_username = (strpos($textmassage, '@') !== false);
$has_link = (strpos($textmassage, 'http') !== false || strpos($textmassage, 'www.') !== false || strpos($textmassage, '.com') !== false || strpos($textmassage, '.ir') !== false || strpos($textmassage, 't.me') !== false);
$has_forbidden_chars = (preg_match('/[<>\/\\\\|*?:"~`]/', $textmassage));

if($has_username || $has_link || $has_forbidden_chars){
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

🚫 نام مستعار نمی‌تواند شامل موارد زیر باشد:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
elseif($nickname_length >= 3 && $nickname_length <= 22){
// ذخیره نام مستعار در فایل کاربر
$juser["userfild"]["nickname"] = $textmassage;
$juser["userfild"]["step"] = "none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ نام مستعار شما با موفقیت ثبت شد!

🗄 نام مستعار جدید: $textmassage

می‌توانید از طریق دکمه زیر به تنظیمات کاربری برگردید.",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"]
		],
		[
		['text'=>"🔙 منوی اصلی",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

نام مستعار باید بین 3 تا 22 کاراکتر باشد.
تعداد کاراکترهای ارسالی شما: $nickname_length

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"🎈 لطفا نام مستعار را به صورت متن ارسال کنید!",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == "search_user") {
if($textmassage == true){
// بررسی طول توکن
if(strlen($textmassage) >= 5 && strlen($textmassage) <= 10 && ctype_alnum($textmassage)) {
    // جستجوی کاربر با توکن
    $userInfo = findUserByToken($textmassage);

    if($userInfo) {
        $targetUser = $userInfo['data'];
        $targetUserId = $userInfo['user_id'];

        // بررسی حریم خصوصی کاربر هدف
        $privacy = isset($targetUser['userfild']['privacy']) ? $targetUser['userfild']['privacy'] : 'غیرفعال';

        if($privacy == 'فعال' && $targetUserId != $from_id) {
            // کاربر حریم خصوصی فعال دارد
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"🔒 این کاربر حریم خصوصی فعال دارد و پروفایل او قابل مشاهده نیست.",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                        ],
                    ]
                ])
            ]);
        } else {
            // نمایش پروفایل کاربر (استفاده از همان کد موجود در /u)
            $baseNickname = !empty($targetUser['userfild']['nickname']) ? $targetUser['userfild']['nickname'] : "ناشناس";
            $gender = !empty($targetUser['userfild']['gender']) ? $targetUser['userfild']['gender'] : "تنظیم نشده";
            $city = !empty($targetUser['userfild']['city']) ? $targetUser['userfild']['city'] : "تنظیم نشده";
            $age = !empty($targetUser['userfild']['age']) ? $targetUser['userfild']['age']." سال" : "تنظیم نشده";
            $score = isset($targetUser['userfild']['score']) ? $targetUser['userfild']['score'] : "5";
            $coins = isset($targetUser['userfild']['coins']) ? $targetUser['userfild']['coins'] : "20";
            $join_date = !empty($targetUser['userfild']['join_date']) ? $targetUser['userfild']['join_date'] : "نامشخص";

            // بررسی وضعیت VIP کاربر
            $isVIP = false;
            if (isset($targetUser['userfild']['subscription']) &&
                isset($targetUser['userfild']['subscription']['is_active']) &&
                $targetUser['userfild']['subscription']['is_active'] &&
                isset($targetUser['userfild']['subscription']['end_date']) &&
                $targetUser['userfild']['subscription']['end_date'] > time()) {
                $isVIP = true;
            }

            $nickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;
            $vipStatus = $isVIP ? "\n✱ این کاربر VIP است" : "";
            $userToken = isset($targetUser['userfild']['user_token']) ? $targetUser['userfild']['user_token'] : "نامشخص";
            $lastVisit = getLastVisitStatus($targetUser);

            $profileCaption = "👤 پروفایل کاربر

✱ نام مستعار : $nickname$vipStatus
✱ توکن کاربر : <a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>
✱ آخرین بازدید : $lastVisit
✱ تاریخ ورود : $join_date
✱ جنسیت : $gender
✱ شهر : $city
✱ سن : $age
✱ امتیاز : $score
✱ سکه : $coins";

            // بررسی تصویر ذخیره شده کاربر
            $userPhoto = isset($targetUser['userfild']['photo']) ? $targetUser['userfild']['photo'] : "";

            if (!empty($userPhoto)) {
                // کاربر تصویر ذخیره شده دارد
                jijibot('sendPhoto', [
                    'chat_id' => $chat_id,
                    'photo' => $userPhoto,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                                ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                            ],
                            [
                                ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                                ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                            ],
                            [
                                ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                                ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                            ],
                            [
                                ['text' => "❌ بستن", 'callback_data' => "close"]
                            ],
                        ]
                    ])
                ]);
            } else {
                // استفاده از تصویر پیش‌فرض
                $defaultPhotoUrl = "https://t.me/fjw2w9c9/1855";

                jijibot('sendPhoto', [
                    'chat_id' => $chat_id,
                    'photo' => $defaultPhotoUrl,
                    'caption' => $profileCaption,
                    'parse_mode' => 'HTML',
                    'reply_markup' => json_encode([
                        'inline_keyboard' => [
                            [
                                ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                                ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                            ],
                            [
                                ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                                ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                            ],
                            [
                                ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                                ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                            ],
                            [
                                ['text' => "❌ بستن", 'callback_data' => "close"]
                            ],
                        ]
                    ])
                ]);
            }
        }

        // ریست کردن step
        $juser["userfild"]["step"] = "none";
        $juser = json_encode($juser,true);
        file_put_contents("data/user/$from_id.json",$juser);

    } else {
        // توکن نامعتبر
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ توکن کاربری نامعتبر!

لطفا توکن صحیح را وارد کنید.
مثال: Abc123X

✍️ دوباره توکن را ارسال کنید:",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    }
} else {
    // فرمت نادرست
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ فرمت توکن نادرست!

توکن باید بین 5 تا 10 کاراکتر باشد.
مثال: Abc12 یا Abc123XY

✍️ دوباره توکن را ارسال کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"🎈 لطفا توکن را به صورت متن ارسال کنید!",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == "set_nickname_for_game") {
if($textmassage == true){
$nickname_length = mb_strlen($textmassage, 'UTF-8');

// بررسی وجود یوزرنیم، لینک و کاراکترهای غیرمجاز
$has_username = (strpos($textmassage, '@') !== false);
$has_link = (strpos($textmassage, 'http') !== false || strpos($textmassage, 'www.') !== false || strpos($textmassage, '.com') !== false || strpos($textmassage, '.ir') !== false || strpos($textmassage, 't.me') !== false);
$has_forbidden_chars = (preg_match('/[<>\/\\\\|*?:"~`]/', $textmassage));

if($has_username || $has_link || $has_forbidden_chars){
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

🚫 نام مستعار نمی‌تواند شامل موارد زیر باشد:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
elseif($nickname_length >= 3 && $nickname_length <= 22){
// ذخیره نام مستعار و شروع بازی
$targetUserId = $juser["userfild"]["game_target"];
$juser["userfild"]["nickname"] = $textmassage;
$juser["userfild"]["step"] = "none";
unset($juser["userfild"]["game_target"]);
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ نام مستعار شما ثبت شد!

🎮 در حال شروع بازی...",
        'reply_markup'=>json_encode([
            'remove_keyboard'=>true
        ])
    ]);

// شروع بازی دوستانه
// دریافت اطلاعات کاربر هدف
$targetUserData = json_decode(file_get_contents("data/user/$targetUserId.json"), true);
$targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";
$targetToken = isset($targetUserData['userfild']['user_token']) ? $targetUserData['userfild']['user_token'] : "نامشخص";

jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"🌟 بازی جدید با کاربر $targetNickname (<a href=\"https://t.me/jdarebot?start=$targetToken\">$targetToken</a>)

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
    'parse_mode'=>'HTML',
    'reply_markup'=>json_encode([
        'keyboard'=>[
            [
                ['text'=>"👀 مشاهده حریف"],
                ['text'=>"❌ پایان بازی"]
            ],
        ],
        'resize_keyboard'=>true
    ])
]);

$name = str_replace(["`","*","_","[","]"],["","","","",""],$first_name);
jijibot('sendmessage',[
    'chat_id'=>$targetUserId,
    'text'=>"🌟 کاربر [$name](tg://user?id=$from_id) با استفاده از لینک دعوت شما وارد ربات شده

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
    'parse_mode'=>'MarkDown',
    'reply_markup'=>json_encode([
        'keyboard'=>[
            [
                ['text'=>"👀 مشاهده حریف"],
                ['text'=>"❌ پایان بازی"]
            ],
        ],
        'resize_keyboard'=>true
    ])
]);

// ادامه منطق بازی...
$array = array("$from_id",$targetUserId);
$random = array_rand($array);
jijibot('sendmessage',[
    'chat_id'=>$array[$random],
    'text'=>"✨ نوبت شما است که سوال بپرسید

لطفا منتظر بمانید تا حریف شما جرعت یا حقیقت را انتخاب کند.",
]);
$result = array_diff($array , array($array[$random]));
jijibot('sendmessage',[
    'chat_id'=>$result[0],
    'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
            ],
        ]
    ])
]);
jijibot('sendmessage',[
    'chat_id'=>$result[1],
    'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
            ],
        ]
    ])
]);

// بازخوانی اطلاعات کاربر و تنظیم وضعیت بازی
$juser = json_decode(file_get_contents("data/user/$from_id.json"), true);
$juser["userfild"]["rival"]="$targetUserId";
$juser["userfild"]["ingame"]="on";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);
$userrival = $targetUserId;
$getrival = json_decode(file_get_contents("data/user/$userrival.json"),true);
$getrival["userfild"]["rival"]="$from_id";
$getrival["userfild"]["ingame"]="on";
$getrival = json_encode($getrival,true);
file_put_contents("data/user/$userrival.json",$getrival);

// تنظیم زمان شروع بازی برای هر دو کاربر
setGameStartTime($from_id);
setGameStartTime($userrival);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

نام مستعار باید بین 3 تا 22 کاراکتر باشد.
تعداد کاراکترهای ارسالی شما: $nickname_length

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"🎈 لطفا نام مستعار را به صورت متن ارسال کنید!",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == "set_nickname_for_gamerandom") {
if($textmassage == true){
$nickname_length = mb_strlen($textmassage, 'UTF-8');

// بررسی وجود یوزرنیم، لینک و کاراکترهای غیرمجاز
$has_username = (strpos($textmassage, '@') !== false);
$has_link = (strpos($textmassage, 'http') !== false || strpos($textmassage, 'www.') !== false || strpos($textmassage, '.com') !== false || strpos($textmassage, '.ir') !== false || strpos($textmassage, 't.me') !== false);
$has_forbidden_chars = (preg_match('/[<>\/\\\\|*?:"~`]/', $textmassage));

if($has_username || $has_link || $has_forbidden_chars){
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

🚫 نام مستعار نمی‌تواند شامل موارد زیر باشد:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
elseif($nickname_length >= 3 && $nickname_length <= 22){
// ذخیره نام مستعار و شروع بازی
$juser["userfild"]["nickname"] = $textmassage;
$juser["userfild"]["step"] = "none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ نام مستعار شما ثبت شد!

🎮 انتخاب تعداد بازیکن

چند نفره می‌خواهید بازی کنید؟",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"👤 انفرادی",'callback_data'=>"game_players_1"]
                ],
                [
                    ['text'=>"👥 4 نفره",'callback_data'=>"game_players_4"],
                    ['text'=>"👥 3 نفره",'callback_data'=>"game_players_3"]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"gamerandom"]
                ],
            ]
        ])
    ]);







}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

نام مستعار باید بین 3 تا 22 کاراکتر باشد.
تعداد کاراکترهای ارسالی شما: $nickname_length

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"⚠️ لطفا نام مستعار خود را به صورت متن ارسال کنید!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
}

elseif ($juser["userfild"]["step"] == "set_nickname_for_three_player") {
if($textmassage == true){
$nickname_length = mb_strlen($textmassage, 'UTF-8');

// بررسی وجود یوزرنیم، لینک و کاراکترهای غیرمجاز
$has_username = (strpos($textmassage, '@') !== false);
$has_link = (strpos($textmassage, 'http') !== false || strpos($textmassage, 'www.') !== false || strpos($textmassage, '.com') !== false || strpos($textmassage, '.ir') !== false || strpos($textmassage, 't.me') !== false);
$has_forbidden_chars = (preg_match('/[<>\/\\\\|*?:"~`]/', $textmassage));

if($has_username || $has_link || $has_forbidden_chars){
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

🚫 نام مستعار نمی‌تواند شامل موارد زیر باشد:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
elseif($nickname_length >= 3 && $nickname_length <= 22){
// ذخیره نام مستعار و شروع بازی 3 نفره
$juser["userfild"]["nickname"] = $textmassage;
$juser["userfild"]["step"] = "none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ نام مستعار شما ثبت شد!

🎉 شروع بازی 3 نفره!

🔍 در حال جستجو برای یافتن 2 بازیکن دیگر...
⏳ لطفا صبر کنید...

💡 در بازی 3 نفره جنسیت مهم نیست و بازیکنان به صورت رندوم انتخاب می‌شوند.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_three_player_search"]
                ],
            ]
        ])
    ]);

// اضافه کردن کاربر به صف بازی 3 نفره
addToThreePlayerQueue($from_id);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

نام مستعار باید بین 3 تا 22 کاراکتر باشد.
تعداد کاراکترهای ارسالی شما: $nickname_length

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
else
{
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"❌ خطا

در این مرحله تنها ارسال متن به عنوان نام مستعار ممکن است.",
 ]);
}
}

elseif ($juser["userfild"]["step"] == "set_nickname_for_four_player") {
if($textmassage == true){
$nickname_length = mb_strlen($textmassage, 'UTF-8');

// بررسی وجود یوزرنیم، لینک و کاراکترهای غیرمجاز
$has_username = (strpos($textmassage, '@') !== false);
$has_link = (strpos($textmassage, 'http') !== false || strpos($textmassage, 'www.') !== false || strpos($textmassage, '.com') !== false || strpos($textmassage, '.ir') !== false || strpos($textmassage, 't.me') !== false);
$has_forbidden_chars = (preg_match('/[<>\/\\\\|*?:"~`]/', $textmassage));

if($has_username || $has_link || $has_forbidden_chars){
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

🚫 نام مستعار نمی‌تواند شامل موارد زیر باشد:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
elseif($nickname_length >= 3 && $nickname_length <= 22){
// ذخیره نام مستعار و شروع بازی 4 نفره
$juser["userfild"]["nickname"] = $textmassage;
$juser["userfild"]["step"] = "none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ نام مستعار شما ثبت شد!

🎉 شروع بازی 4 نفره!

🔍 در حال جستجو برای یافتن 3 بازیکن دیگر...
⏳ لطفا صبر کنید...

💡 در بازی 4 نفره جنسیت مهم نیست و بازیکنان به صورت رندوم انتخاب می‌شوند.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"❌ لغو جستجو",'callback_data'=>"cancel_four_player_search"]
                ],
            ]
        ])
    ]);

// اضافه کردن کاربر به صف بازی 4 نفره
addToFourPlayerQueue($from_id);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

نام مستعار باید بین 3 تا 22 کاراکتر باشد.
تعداد کاراکترهای ارسالی شما: $nickname_length

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
else
{
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"❌ خطا

در این مرحله تنها ارسال متن به عنوان نام مستعار ممکن است.",
 ]);
}
}

elseif ($juser["userfild"]["step"] == "set_nickname_for_gamebylink") {
if($textmassage == true){
$nickname_length = mb_strlen($textmassage, 'UTF-8');

// بررسی وجود یوزرنیم، لینک و کاراکترهای غیرمجاز
$has_username = (strpos($textmassage, '@') !== false);
$has_link = (strpos($textmassage, 'http') !== false || strpos($textmassage, 'www.') !== false || strpos($textmassage, '.com') !== false || strpos($textmassage, '.ir') !== false || strpos($textmassage, 't.me') !== false);
$has_forbidden_chars = (preg_match('/[<>\/\\\\|*?:"~`]/', $textmassage));

if($has_username || $has_link || $has_forbidden_chars){
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

🚫 نام مستعار نمی‌تواند شامل موارد زیر باشد:
• یوزرنیم (@username)
• لینک و آدرس وب‌سایت
• کاراکترهای ویژه: < > / \\ | * ? : \" ~ `

لطفا نام مستعار مناسب انتخاب کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
elseif($nickname_length >= 3 && $nickname_length <= 22){
// ذخیره نام مستعار و ایجاد لینک بازی
$juser["userfild"]["nickname"] = $textmassage;
$juser["userfild"]["step"] = "none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

// تولید کد رندوم برای بازی
$gameCode = generateRandomGameCode();
saveGameCode($from_id, $gameCode);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✨ لینک دعوت شما با موفقیت ساخته شد.

✱  لینک دعوت شما :

<blockquote>https://t.me/$usernamebot?start=p$gameCode</blockquote>

✱  شما میتوانید با اشتراک گذاری لینک خود دوستانتان را به بازی دعوت کنید.
✱  مدت اعتبار این لینک : 24 ساعت
✱ از طریق دکمه زیر می توانید لینک را به اشتراک بگذارید.",
'parse_mode'=>'HTML',
'disable_web_page_preview'=>true,
'reply_markup'=>json_encode([
    'remove_keyboard'=>true,
    'inline_keyboard'=>[
		[
	['text'=>"📤  اشتراک گذاری",'switch_inline_query'=>"invites you\n🎮 جرعت حقیقت را با امنیت و سرعت بالا بازی کن!\n✱ شما میتوانید همین حالا از طریق لینک زیر بازی را با دوست خود شروع کنید.\ntelegram.me/$usernamebot?start=p$gameCode"]
	],
		[
	['text'=>"🔙 برگشت",'callback_data'=>"back"]
	],
              ]
        ])
    ]);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم نام مستعار

نام مستعار باید بین 3 تا 22 کاراکتر باشد.
تعداد کاراکترهای ارسالی شما: $nickname_length

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"🎈 لطفا نام مستعار را به صورت متن ارسال کنید!",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 بازگشت",'callback_data'=>"back"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == "set_age") {
if($textmassage == true){
// بررسی اینکه ورودی فقط عدد باشد
if(is_numeric($textmassage) && ctype_digit($textmassage)){
$age = intval($textmassage);
if($age >= 18 && $age <= 50){
// ذخیره سن در فایل کاربر
$juser["userfild"]["age"] = $age;
$juser["userfild"]["step"] = "none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);

jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ سن شما با موفقیت ثبت شد!

🎚 سن: $age سال

می‌توانید از طریق دکمه زیر به تنظیمات کاربری برگردید.",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"],['text'=>"🗄 ثبت اطلاعات",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم سن

سن باید بین 18 تا 50 سال باشد.
سن ارسالی شما: $age

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ خطا در تنظیم سن

لطفا فقط عدد ارسال کنید.
مثال: 25

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"🎈 لطفا سن را به صورت عدد ارسال کنید!

مثال: 25",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"register_info"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == "set_custom_token") {
if($textmassage == true){
// بررسی طول توکن (5 تا 10 کاراکتر)
if(strlen($textmassage) >= 5 && strlen($textmassage) <= 10) {
    // بررسی اینکه فقط حروف انگلیسی و اعداد باشد
    if(ctype_alnum($textmassage)) {
        // بررسی یکتا بودن توکن
        if(isTokenUnique($textmassage)) {
            // ذخیره توکن اختصاصی
            $juser["userfild"]["user_token"] = $textmassage;
            $juser["userfild"]["step"] = "none";
            $juser = json_encode($juser,true);
            file_put_contents("data/user/$from_id.json",$juser);

            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"✅ توکن اختصاصی شما با موفقیت تنظیم شد!

🔑 توکن جدید: $textmassage

حالا کاربران می‌توانند با این توکن پروفایل شما را مشاهده کنند.",
               'reply_markup'=>json_encode([
                   'inline_keyboard'=>[
            			[
            		['text'=>"⚙️ تنظیمات کاربری",'callback_data'=>"user_settings"]
            		],
                          ]
                    ])
                ]);
        } else {
            // توکن تکراری است
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"❌ این توکن قبلاً توسط کاربر دیگری انتخاب شده است!

لطفا توکن دیگری انتخاب کنید:
• حداقل 5 کاراکتر و حداکثر 10 کاراکتر
• فقط حروف انگلیسی و اعداد
• مثال: MyToken123",
               'reply_markup'=>json_encode([
                   'inline_keyboard'=>[
            			[
            		['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
            		],
                          ]
                    ])
                ]);
        }
    } else {
        // کاراکترهای غیرمجاز
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ توکن فقط می‌تواند شامل حروف انگلیسی و اعداد باشد!

لطفا توکن معتبر ارسال کنید:
• حداقل 5 کاراکتر و حداکثر 10 کاراکتر
• فقط حروف انگلیسی و اعداد
• مثال: MyToken123",
           'reply_markup'=>json_encode([
               'inline_keyboard'=>[
        			[
        		['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
        		],
                      ]
                ])
            ]);
    }
} else {
    // طول نامعتبر
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ طول توکن نامعتبر!

توکن باید بین 5 تا 10 کاراکتر باشد.
طول توکن ارسالی شما: ".strlen($textmassage)." کاراکتر

لطفا دوباره تلاش کنید:",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
    			[
    		['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
    		],
                  ]
            ])
        ]);
}
}
else
{
jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"🎈 لطفا توکن را به صورت متن ارسال کنید!",
       'reply_markup'=>json_encode([
           'inline_keyboard'=>[
			[
		['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
		],
                  ]
            ])
        ]);
}
}
elseif ($juser["userfild"]["step"] == 'phone_verification') {
if($textmassage == true){
// بررسی فرمت شماره تلفن
if(preg_match('/^09[0-9]{9}$/', $textmassage)){
    $current = $textmassage;

    // بررسی سیستم انتی اسپم شماره تلفن
    $currentTime = time();
    $phoneAttempts = isset($juser["userfild"]["phone_attempts"]) ? $juser["userfild"]["phone_attempts"] : [];
    $attemptCount = count($phoneAttempts);

    // بررسی آخرین تلاش
    if($attemptCount > 0) {
        $lastAttempt = end($phoneAttempts);
        $timeDiff = $currentTime - $lastAttempt;

        // تعیین زمان انتظار بر اساس تعداد تلاش‌ها
        $waitTime = 0;
        $waitMessage = "";

        if($attemptCount == 1) {
            $waitTime = 120; // 2 دقیقه
            $waitMessage = "2 دقیقه";
        } elseif($attemptCount == 2) {
            $waitTime = 600; // 10 دقیقه
            $waitMessage = "10 دقیقه";
        } elseif($attemptCount == 3) {
            $waitTime = 3600; // 60 دقیقه
            $waitMessage = "60 دقیقه";
        } elseif($attemptCount >= 4) {
            // مسدود کامل
            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"🚫 مسدود شده

شما بیش از حد مجاز تلاش کرده‌اید. امکان تایید شماره تلفن برای شما غیرفعال شده است.

لطفاً با پشتیبانی تماس بگیرید.",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"☎️ پشتیبانی",'callback_data'=>"support"],
                            ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
                        ],
                    ]
                ])
            ]);
            return;
        }

        // بررسی اینکه آیا زمان انتظار گذشته است یا نه
        if($timeDiff < $waitTime) {
            $remainingTime = $waitTime - $timeDiff;
            $remainingMinutes = ceil($remainingTime / 60);

            jijibot('sendmessage',[
                'chat_id'=>$chat_id,
                'text'=>"⏳ محدودیت زمانی

شما اخیراً شماره تلفن ارسال کرده‌اید. لطفاً $remainingMinutes دقیقه دیگر تلاش کنید.

⚠️ تلاش بعدی شما محدودیت $waitMessage خواهد داشت.",
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
                        ],
                    ]
                ])
            ]);
            return;
        }
    }

    // ثبت تلاش جدید
    $phoneAttempts[] = $currentTime;
    $juser["userfild"]["phone_attempts"] = $phoneAttempts;

    // Generate code
    $code = rand(1000, 9999);
    Session::set($from_id, 'phone_verification_code', $code);
    Session::set($from_id, 'phone_verification_number', $current);

    // Format phone number to +989xxxxxxxxx format (remove leading 0)
    $formatted_phone = '+98' . substr($current, 1);

    // Send code via sender.php (HTTP POST)
    $ch = curl_init('https://myhtg.ir/OTP/send.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query([
        'phone' => $formatted_phone,
        'code' => $code,
        'action' => 'send_sms'
    ]));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ ارسال موفق کد تایید

لطفا کد 4 رقمی دریافتی به شماره $current را در چت ارسال کنید:",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
                ],
            ]
        ])
    ]);

    $juser["userfild"]["step"]="code_verification";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);
}
else
{
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ فرمت نادرست

شما می بایست شماره تلفن خود را با فرمت 09xxxxxxxxx ارسال کنید",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
                ],
            ]
        ])
    ]);
}
}
else
{
jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"📱 لطفا شماره تلفن خود را به صورت متن ارسال کنید!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
            ],
        ]
    ])
]);
}
}
elseif ($juser["userfild"]["step"] == 'code_verification') {
if($textmassage == true){
// بررسی کد تایید
$stored_code = Session::get($from_id, 'phone_verification_code');
$stored_phone = Session::get($from_id, 'phone_verification_number');
$selected_plan = Session::get($from_id, 'selected_plan');
$plan_days = Session::get($from_id, 'plan_days');
$plan_price = Session::get($from_id, 'plan_price');

if($textmassage == $stored_code){
    // کد صحیح است - ایجاد لینک پرداخت
    $price = getPlanPrice($selected_plan);
    $amount = convertToRials($price);
    $paymentLink = generatePaymentLink($from_id, $amount, $selected_plan);

    // تعیین متن بر اساس نوع پلن
    $planText = "";
    switch($selected_plan) {
        case 'sub_30':
            $planText = "💳 اشتراک 30روزه : 21,000 تومان\n\nبا خرید این اشتراک، به مدت 30 روز می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
        case 'sub_60':
            $planText = "💳 اشتراک 60روزه : 42,000 تومان\n\nبا خرید این اشتراک، به مدت 60 روز می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
        case 'sub_90':
            $planText = "💳 اشتراک 90روزه : 63,000 تومان\n\nبا خرید این اشتراک، به مدت 90 روز می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
        case 'sub_365':
            $planText = "💳 اشتراک 1 ساله : 252,000 تومان\n\nبا خرید این اشتراک، به مدت 1 سال می‌توانید از تمامی امکانات ربات استفاده کنید.";
            break;
    }

    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"✅ شماره تلفن با موفقیت تایید شد!\n\n".$planText."\n\n<b>⚠️ برای پرداخت اتصال VPN خود را قطع نمائید.</b>",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"💳 پرداخت",'url'=>$paymentLink]
                ],
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"subscription"]
                ]
            ]
        ])
    ]);

    // ذخیره شماره تلفن در دیتابیس کاربر
    $juser["userfild"]["phone"] = $stored_phone;

    // پاک کردن session ها
    Session::delete($from_id, 'phone_verification_code');
    Session::delete($from_id, 'phone_verification_number');
    Session::delete($from_id, 'selected_plan');
    Session::delete($from_id, 'plan_days');
    Session::delete($from_id, 'plan_price');

    $juser["userfild"]["step"]="none";
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);
}
else
{
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ کد نادرست

 لطفا کد 4 رقمی پیامک شده بر شماره تلفن را ارسال کنید :",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
                ],
            ]
        ])
    ]);
}
}
else
{
jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"🔢 لطفا کد 4 رقمی را به صورت متن ارسال کنید!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 برگشت",'callback_data'=>"subscription"]
            ],
        ]
    ])
]);
}
}
elseif ($juser["userfild"]["step"] == 'support') {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"✨ پیام شما به پشتیبانی ارسال شد، منتظر پاسخ باشید.",
               'reply_markup'=>json_encode([
                   'inline_keyboard'=>[
				   [
['text'=>"🔙 برگشت",'callback_data'=>'back']
				   ],
                     ]
               ])
 ]);
jijibot('ForwardMessage',[
'chat_id'=>$Dev[0],
'from_chat_id'=>$chat_id,
'message_id'=>$message_id
]);
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);
}
elseif ($juser["userfild"]["step"] == 'submit_dare_text') {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"✅ جرعت شما با موفقیت ثبت شد!

📝 جرعت ارسالی: $textmassage

🔍 جرعت شما توسط تیم بررسی و در صورت تایید به ربات اضافه خواهد شد.

🙏 از مشارکت شما سپاسگزاریم!",
               'reply_markup'=>json_encode([
                   'inline_keyboard'=>[
				   [
['text'=>"🔙 برگشت",'callback_data'=>'back']
				   ],
                     ]
               ])
 ]);
jijibot('sendmessage',[
'chat_id'=>$Dev[0],
'text'=>"💪 جرعت جدید ثبت شده:

👤 از کاربر: $first_name (@$username)
🆔 آیدی: $from_id

📝 متن جرعت:
$textmassage"
]);
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);
}
elseif ($juser["userfild"]["step"] == 'submit_truth_text') {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"✅ حقیقت شما با موفقیت ثبت شد!

📝 حقیقت ارسالی: $textmassage

🔍 حقیقت شما توسط تیم بررسی و در صورت تایید به ربات اضافه خواهد شد.

🙏 از مشارکت شما سپاسگزاریم!",
               'reply_markup'=>json_encode([
                   'inline_keyboard'=>[
				   [
['text'=>"🔙 برگشت",'callback_data'=>'back']
				   ],
                     ]
               ])
 ]);
jijibot('sendmessage',[
'chat_id'=>$Dev[0],
'text'=>"💬 حقیقت جدید ثبت شده:

👤 از کاربر: $first_name (@$username)
🆔 آیدی: $from_id

📝 متن حقیقت:
$textmassage"
]);
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);
}
elseif ($juser["userfild"]["step"] == 'sup') {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"📍 پیام شما ارسال شد منتظر پاسخ باشید",
               'reply_markup'=>json_encode([
                   'inline_keyboard'=>[
				   [
['text'=>"🔙 برگشت",'callback_data'=>'back']
				   ],
                     ]
               ])
 ]);
jijibot('ForwardMessage',[
'chat_id'=>$Dev[0],
'from_chat_id'=>$chat_id,
'message_id'=>$message_id
]);
}
//==============================================================
//panel admin
elseif($textmassage=="/panel" or $textmassage=="panel" or $textmassage=="مدیریت"){
if ($tc == "private") {
if (in_array($from_id,$Dev)){
jijibot('sendmessage',[
'chat_id'=>$chat_id,
'text'=>"🚦 ادمین عزیز به پنل مدریت ربات خوش امدید",
	  'reply_markup'=>json_encode([
    'keyboard'=>[
	  	  	 [
		['text'=>"📍 امار ربات"]             
		 ],
		 		  	[
	  	['text'=>"📍 افزودن حقیقت"],['text'=>"📍 افزودن جرعت"]
	  ],
		  	[
	  	['text'=>"📍 ارسال به گروه ها"],['text'=>"📍 فوروارد به گروه ها"]
	  ],
 	[
	  	['text'=>"📍 ارسال به کاربران"],['text'=>"📍 فوروارد به کاربران"]
	  ],
   ],
      'resize_keyboard'=>true
   ])
 ]);
}
}
}
elseif($textmassage=="برگشت 🔙"){
if ($tc == "private") {
if (in_array($from_id,$Dev)){
jijibot('sendmessage',[
'chat_id'=>$chat_id,
'text'=>"🚦 به منوی مدیریت بازگشتید",
	  'reply_markup'=>json_encode([
    'keyboard'=>[
	  	  	 [
		['text'=>"📍 امار ربات"]             
		 ],
		 		  	[
	  	['text'=>"📍 افزودن حقیقت"],['text'=>"📍 افزودن جرعت"]
	  ],
		  	[
	  	['text'=>"📍 ارسال به گروه ها"],['text'=>"📍 فوروارد به گروه ها"]
	  ],
 	[
	  	['text'=>"📍 ارسال به کاربران"],['text'=>"📍 فوروارد به کاربران"]
	  ],
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
}
elseif($textmassage=="📍 امار ربات"){
if (in_array($from_id,$Dev)){
$getuser = scandir("data/user/");
$alluser = count($getuser) - 1;
$getgp = scandir("data/gp/");
$allgp = count($getgp) - 1;
$allhagh1 = count($database["ha"]["normal"]);
$allhagh2 = count($database["ha"]["normal"]["boy"]);
$allhagh3 = count($database["ha"]["normal"]["girl"]);
$alljorat1 = count($database["jo"]["normal"]);
$alljorat2 = count($database["jo"]["plus"]["boy"]);
$alljorat3 = count($database["jo"]["plus"]["girl"]);
				jijibot('sendmessage',[
		'chat_id'=>$chat_id,
		'text'=>"🤖 امار ربات شما : 
		
📍تعداد عضو ها : $alluser
📍تعداد گروه ها : $allgp
📍تعداد جرعت ها : $alljorat1 | $alljorat2 | $alljorat3
📍تعداد حقیقت ها : $allhagh1 | $allhagh2 | $allhagh3",
		]);
		}
}
elseif ($textmassage == '📍 افزودن جرعت' ) {
if (in_array($from_id,$Dev)){
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"لطفا سوال مربوط به جرعت را ارسال کنید 🚀",
	  'reply_to_message_id'=>$message_id,
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="setju";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
elseif ($juser["userfild"]["step"] == 'setju') {
if ($textmassage != "برگشت 🔙") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"ذخیر شد  ✅
			
📍 سوال اضافه شده مربوط ب کدام بخش است ؟
normal  = عادی 
plus = +18",
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"normal"],['text'=>"plus"]
	],
			[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="setju2";
$juser["userfild"]["qu"]="$textmassage";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
elseif ($juser["userfild"]["step"] == 'setju2') {
$qu = $juser["userfild"]["qu"];
if ($textmassage != "برگشت 🔙") {
if ($textmassage == "normal") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"با موفقیت اضافه شد  ✅
			
📍 در صورتی که میخواهید سوال جرعت دیگری رو اضافه کنید ان را ارسال کنید",
 ]);
$database["jo"]["normal"][]="$qu";
$database = json_encode($database,true);
file_put_contents("data/database.json",$database);
$juser["userfild"]["step"]="setju";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
if ($textmassage == "plus") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"📍 مربوط ب کدام جنسیت ؟ 
boy  = دختر 
girl = پسر",
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"boy"],['text'=>"girl"]
	],
		[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="setju3";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
}
elseif ($juser["userfild"]["step"] == 'setju3') {
$qu = $juser["userfild"]["qu"];
if ($textmassage != "برگشت 🔙") {
if ($textmassage == "boy" or $textmassage == "girl") {
$stats = $juser["userfild"]["stats"];
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"با موفقیت اضافه شد  ✅
			
📍 در صورتی که میخواهید سوال جرعت دیگری رو اضافه کنید ان را ارسال کنید",
 ]);
$database["jo"]["plus"]["$textmassage"][]="$qu";
$database = json_encode($database,true);
file_put_contents("data/database.json",$database);
$juser["userfild"]["step"]="setju";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
}
elseif ($textmassage == '📍 افزودن حقیقت' ) {
if (in_array($from_id,$Dev)){
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"لطفا سوال مربوط به حقیقت رو ارسال کنید 🚀",
	  'reply_to_message_id'=>$message_id,
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="setha";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
elseif ($juser["userfild"]["step"] == 'setha') {
if ($textmassage != "برگشت 🔙") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"ذخیر شد  ✅
			
📍 سوال اضافه شده مربوط ب کدام بخش است ؟
normal  = عادی 
plus = +18",
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"normal"],['text'=>"plus"]
	],
			[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="setha2";
$juser["userfild"]["qu"]="$textmassage";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
elseif ($juser["userfild"]["step"] == 'setha2') {
$qu = $juser["userfild"]["qu"];
if ($textmassage != "برگشت 🔙") {
if ($textmassage == "normal") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"با موفقیت اضافه شد  ✅
			
📍 در صورتی که میخواهید سوال جرعت دیگری رو اضافه کنید ان را ارسال کنید",
 ]);
$database["ha"]["normal"][]="$qu";
$database = json_encode($database,true);
file_put_contents("data/database.json",$database);
$juser["userfild"]["step"]="setha";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
if ($textmassage == "plus") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"📍 مربوط ب کدام جنسیت ؟ 
boy  = دختر 
girl = پسر",
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"boy"],['text'=>"girl"]
	],
		[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="setha3";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
}
elseif ($juser["userfild"]["step"] == 'setha3') {
$qu = $juser["userfild"]["qu"];
if ($textmassage != "برگشت 🔙") {
if ($textmassage == "boy" or $textmassage == "girl") {
$stats = $juser["userfild"]["stats"];
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"با موفقیت اضافه شد  ✅
			
📍 در صورتی که میخواهید سوال جرعت دیگری رو اضافه کنید ان را ارسال کنید",
 ]);
$database["ha"]["plus"]["$textmassage"][]="$qu";
$database = json_encode($database,true);
file_put_contents("data/database.json",$database);
$juser["userfild"]["step"]="setha";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
}
elseif ($textmassage == '📍 ارسال به کاربران' ) {
if (in_array($from_id,$Dev)){
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"لطفا متن خود را ارسال کنید 🚀",
	  'reply_to_message_id'=>$message_id,
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="sendtoall";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
elseif ($juser["userfild"]["step"] == 'sendtoall') {
if ($textmassage != "برگشت 🔙") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"پیام شما با موفقیت برای ارسال همگانی تنظیم شد  ✔️",
	  'reply_to_message_id'=>$message_id,
 ]);
$inuser = json_decode(file_get_contents("user.json"),true);
$inuser["text"]="$textmassage";
$inuser["sendtoall"]="true";
$inuser = json_encode($inuser,true);
file_put_contents("user.json",$inuser);	
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
elseif ($textmassage == '📍 ارسال به گروه ها' ) {
if (in_array($from_id,$Dev)){
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"لطفا متن خود را ارسال کنید 🚀",
	  'reply_to_message_id'=>$message_id,
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="sendtoallgp";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
elseif ($juser["userfild"]["step"] == 'sendtoallgp') {
if ($textmassage != "برگشت 🔙") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"پیام شما با موفقیت برای ارسال همگانی تنظیم شد  ✔️",
	  'reply_to_message_id'=>$message_id,
 ]);
$inuser = json_decode(file_get_contents("user.json"),true);
$inuser["text"]="$textmassage";
$inuser["sendtoallgp"]="true";
$inuser = json_encode($inuser,true);
file_put_contents("user.json",$inuser);	
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
elseif ($textmassage == '📍 فوروارد به کاربران' ) {
if (in_array($from_id,$Dev)){
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"لطفا متن خود را فوروارد کنید  🚀",
	  'reply_to_message_id'=>$message_id,
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="fortoall";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
elseif ($juser["userfild"]["step"] == 'fortoall') {
if ($textmassage != "برگشت 🔙") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"پیام شما با موفقیت به عنوان فوروارد همگانی تنظیم شد ✔️",
	  'reply_to_message_id'=>$message_id,
 ]);
$inuser = json_decode(file_get_contents("user.json"),true);
$inuser["msg"]="$message_id";
$inuser["chat"]="$chat_id";
$inuser["fortoall"]="true";
$inuser = json_encode($inuser,true);
file_put_contents("user.json",$inuser);	
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
elseif ($textmassage == '📍 فوروارد به گروه ها' ) {
if (in_array($from_id,$Dev)){
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"لطفا متن خود را فوروارد کنید  🚀",
	  'reply_to_message_id'=>$message_id,
	   'reply_markup'=>json_encode([
    'keyboard'=>[
	[
	['text'=>"برگشت 🔙"] 
	]
   ],
      'resize_keyboard'=>true
   ])
 ]);
$juser["userfild"]["step"]="fortoallgp";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);		
}
}
elseif ($juser["userfild"]["step"] == 'fortoallgp') {
if ($textmassage != "برگشت 🔙") {
         jijibot('sendmessage',[
        	'chat_id'=>$chat_id,
        	'text'=>"پیام شما با موفقیت به عنوان فوروارد همگانی تنظیم شد ✔️",
	  'reply_to_message_id'=>$message_id,
 ]);
$inuser = json_decode(file_get_contents("user.json"),true);
$inuser["msg"]="$message_id";
$inuser["chat"]="$chat_id";
$inuser["fortoallgp"]="true";
$inuser = json_encode($inuser,true);
file_put_contents("user.json",$inuser);	
$juser["userfild"]["step"]="none";
$juser = json_encode($juser,true);
file_put_contents("data/user/$from_id.json",$juser);	
}
}
elseif ($juser["userfild"]["step"] == 'gift_code_input') {
if($textmassage == true){
// لیست کدهای هدیه معتبر (می‌توانید این را در فایل جداگانه‌ای نگهداری کنید)
$valid_codes = [
    'SPEEDX2024' => 50,  // کد و مقدار سکه
    'WELCOME100' => 100,
    'DAILY20' => 20
];

$gift_code = strtoupper(trim($textmassage));

if(isset($valid_codes[$gift_code])) {
    // بررسی اینکه کاربر قبلاً این کد را استفاده کرده یا نه
    $used_codes = isset($juser["userfild"]["used_gift_codes"]) ? $juser["userfild"]["used_gift_codes"] : [];

    if(in_array($gift_code, $used_codes)) {
        // کد قبلاً استفاده شده
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ شما قبلاً از این کد هدیه استفاده کرده‌اید!",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 برگشت",'callback_data'=>"coins"]
                    ],
                ]
            ])
        ]);
    } else {
        // کد معتبر و جدید - اعطای سکه
        $coin_reward = $valid_codes[$gift_code];
        $current_coins = isset($juser["userfild"]["coins"]) ? $juser["userfild"]["coins"] : 20;
        $new_coins = $current_coins + $coin_reward;

        // به‌روزرسانی اطلاعات کاربر
        $juser["userfild"]["coins"] = $new_coins;
        if(!isset($juser["userfild"]["used_gift_codes"])) {
            $juser["userfild"]["used_gift_codes"] = [];
        }
        $juser["userfild"]["used_gift_codes"][] = $gift_code;
        $juser["userfild"]["step"] = "none";
        $juser = json_encode($juser,true);
        file_put_contents("data/user/$from_id.json",$juser);

        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"🎉 کد هدیه با موفقیت اعمال شد!

🎁 کد: $gift_code
🪙 سکه دریافتی: $coin_reward
💰 موجودی جدید: $new_coins سکه",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🪙 مدیریت سکه",'callback_data'=>"coins"]
                    ],
                    [
                        ['text'=>"🔙 منوی اصلی",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    }
} else {
    // کد نامعتبر
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ کد هدیه نامعتبر!

لطفا کد صحیح را وارد کنید یا از کانال‌های ما کدهای جدید دریافت کنید.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔄 تلاش مجدد",'callback_data'=>"gift_code"]
                ],
                [
                    ['text'=>"🔙 برگشت",'callback_data'=>"coins"]
                ],
            ]
        ])
    ]);
}
}
else
{
jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"🎁 لطفا کد هدیه خود را به صورت متن ارسال کنید!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 برگشت",'callback_data'=>"coins"]
            ],
        ]
    ])
]);
}
}
elseif(strpos($data, "invite_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// بررسی اینکه آیا کاربر در حال حاضر در بازی است
if(!empty($cuser["userfild"]["rival"]) && $cuser["userfild"]["ingame"] == "on") {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "⚠️ امکان استفاده از دعوت به بازی در حین بازی وجود ندارد.",
        'show_alert' => true
    ]);
    return;
}

// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("invite_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد خودش را دعوت کند
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
    return;
}

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";
    $senderNickname = !empty($cuser['userfild']['nickname']) ? $cuser['userfild']['nickname'] : "ناشناس";

    // بررسی حریم خصوصی کاربر هدف
    $targetPrivacy = isset($targetUserData['userfild']['privacy']) ? $targetUserData['userfild']['privacy'] : 'غیرفعال';

    if($targetPrivacy == 'فعال' && $targetUserId != $fromid) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ این کاربر حریم خصوصی فعال دارد و نمی‌توانید او را به بازی دعوت کنید.",
            'show_alert' => true
        ]);
    } elseif(is_user_blocked($fromid, $targetUserId)) {
        // بررسی مسدودیت
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "🚫 شما توسط این کاربر مسدود شده‌اید و نمی‌توانید دعوت بازی ارسال کنید.",
            'show_alert' => true
        ]);
    } else {
        // بررسی محدودیت زمانی (5 دقیقه)
        $currentTime = time();
        $lastInviteTime = isset($cuser['userfild']['last_invite_'.$targetUserId]) ? $cuser['userfild']['last_invite_'.$targetUserId] : 0;
        $timeDiff = $currentTime - $lastInviteTime;

        // اگر کمتر از 5 دقیقه (300 ثانیه) از آخرین دعوت گذشته باشد
        if($lastInviteTime > 0 && $timeDiff < 300) {
            $remainingTime = 300 - $timeDiff;
            $remainingMinutes = ceil($remainingTime / 60);

            jijibot('answercallbackquery', [
                'callback_query_id' => $membercall,
                'text' => "⏳ شما اخیراً به این کاربر دعوت ارسال کرده‌اید. لطفاً $remainingMinutes دقیقه دیگر تلاش کنید.",
                'show_alert' => true
            ]);
        } else {
            // ارسال دعوت بازی به کاربر هدف
            $senderToken = isset($cuser['userfild']['user_token']) ? $cuser['userfild']['user_token'] : "نامشخص";
            jijibot('sendmessage',[
                'chat_id' => $targetUserId,
                'text' => "🎮 دعوت به بازی!

$senderNickname (<a href=\"https://t.me/jdarebot?start=$senderToken\">$senderToken</a>) شما را به بازی جرعت و حقیقت دعوت کرده است.

آیا می‌خواهید بازی را شروع کنید؟",
                'parse_mode' => 'HTML',
                'disable_web_page_preview' => true,
                'reply_markup' => json_encode([
                    'inline_keyboard' => [
                        [
                            ['text' => "✅ قبول", 'callback_data' => "accept_game_$fromid"],
                            ['text' => "❌ رد", 'callback_data' => "reject_game_$fromid"]
                        ],
                        [
                            ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$fromid"]
                        ]
                    ]
                ])
            ]);

            // ذخیره زمان آخرین دعوت
            $cuser['userfild']['last_invite_'.$targetUserId] = $currentTime;
            $cuser = json_encode($cuser,true);
            file_put_contents("data/user/$fromid.json",$cuser);

            // اطلاع به فرستنده
            jijibot('answercallbackquery', [
                'callback_query_id' => $membercall,
                'text' => "✅ دعوت بازی به $targetNickname ارسال شد!",
                'show_alert' => false
            ]);
        }
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "accept_game_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر دعوت‌کننده
$inviterUserId = str_replace("accept_game_", "", $data);

// حذف پیام دعوت اصلی
$inviterData = json_decode(file_get_contents("data/user/$inviterUserId.json"), true);
if(isset($inviterData['userfild']['invite_message_'.$fromid])) {
    $inviteMessageId = $inviterData['userfild']['invite_message_'.$fromid];
    jijibot('deletemessage',[
        'chat_id' => $fromid,
        'message_id' => $inviteMessageId
    ]);
    // حذف message_id ذخیره شده
    unset($inviterData['userfild']['invite_message_'.$fromid]);
    $inviterData = json_encode($inviterData, true);
    file_put_contents("data/user/$inviterUserId.json", $inviterData);
}

// بررسی اینکه آیا کاربر پذیرنده در بازی است
if(!empty($cuser["userfild"]["rival"]) && $cuser["userfild"]["ingame"] == "on") {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"❌ خطا

شما در حال حاضر در بازی دیگری هستید و نمی‌توانید دعوت جدید بپذیرید.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ]
            ]
        ])
    ]);
    return;
}

// بررسی اینکه آیا دعوت‌کننده در بازی است
$inviterData = json_decode(file_get_contents("data/user/$inviterUserId.json"), true);
if(!empty($inviterData["userfild"]["rival"]) && $inviterData["userfild"]["ingame"] == "on") {
    jijibot('editmessagetext',[
        'chat_id'=>$chatid,
        'message_id'=>$messageid,
        'text'=>"❌ خطا

دعوت‌کننده در حال حاضر در بازی دیگری است و نمی‌توان بازی را شروع کرد.",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                ]
            ]
        ])
    ]);
    return;
}

// دریافت اطلاعات کاربران
$inviterNickname = !empty($inviterData['userfild']['nickname']) ? $inviterData['userfild']['nickname'] : "ناشناس";
$inviterToken = isset($inviterData['userfild']['user_token']) ? $inviterData['userfild']['user_token'] : "نامشخص";
$accepterNickname = !empty($cuser['userfild']['nickname']) ? $cuser['userfild']['nickname'] : "ناشناس";
$accepterToken = isset($cuser['userfild']['user_token']) ? $cuser['userfild']['user_token'] : "نامشخص";

// شروع بازی - ارسال پیام به دعوت‌کننده
jijibot('sendmessage',[
    'chat_id' => $inviterUserId,
    'text' => "🌟 بازی جدید با کاربر $accepterNickname (<a href=\"https://t.me/jdarebot?start=$accepterToken\">$accepterToken</a>)

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
    'parse_mode' => 'HTML',
    'disable_web_page_preview' => true,
    'reply_markup' => json_encode([
        'keyboard' => [
            [
                ['text' => "👀 مشاهده حریف"],
                ['text' => "❌ پایان بازی"]
            ],
        ],
        'resize_keyboard' => true
    ])
]);

// حذف پیام دعوت فعلی
jijibot('deletemessage',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"🌟 بازی جدید با کاربر $inviterNickname (<a href=\"https://t.me/jdarebot?start=$inviterToken\">$inviterToken</a>)

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
    'parse_mode'=>'HTML',
    'disable_web_page_preview'=>true,
    'reply_markup'=>json_encode([
        'remove_keyboard'=>true
    ])
]);

jijibot('sendmessage',[
    'chat_id'=>$fromid,
    'text'=>"🌟 بازی جدید با کاربر $inviterNickname (<a href=\"https://t.me/jdarebot?start=$inviterToken\">$inviterToken</a>)

🔄 در حال پردازش بازی...

ربات در حال قرعه کشی برای بازی می باشد.",
    'parse_mode'=>'HTML',
    'disable_web_page_preview'=>true,
    'reply_markup'=>json_encode([
        'keyboard'=>[
            [
                ['text'=>"👀 مشاهده حریف"],
                ['text'=>"❌ پایان بازی"]
            ],
        ],
        'resize_keyboard'=>true
    ])
]);

// قرعه‌کشی برای تعیین نوبت
$array = array($inviterUserId, $fromid);
$random = array_rand($array);
jijibot('sendmessage',[
    'chat_id'=>$array[$random],
    'text'=>"✨ نوبت شما است که سوال بپرسید

لطفا منتظر بمانید تا حریف شما جرعت یا حقیقت را انتخاب کند.",
]);
$result = array_diff($array , array($array[$random]));
jijibot('sendmessage',[
    'chat_id'=>$result[0],
    'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
            ],
        ]
    ])
]);
jijibot('sendmessage',[
    'chat_id'=>$result[1],
    'text'=>"✨ کدام رو انتخاب می کنید؟

کاربر گرامی یکی را برای ادامه بازی انتخاب کنید:",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"💪🏻 جرعت",'callback_data'=>"jorats"],['text'=>"🗣 حقیقت",'callback_data'=>"haghights"]
            ],
        ]
    ])
]);

// تنظیم وضعیت بازی برای دعوت‌کننده
$inviterData["userfild"]["rival"] = "$fromid";
$inviterData["userfild"]["ingame"] = "on";
$inviterData = json_encode($inviterData, true);
file_put_contents("data/user/$inviterUserId.json", $inviterData);

// تنظیم وضعیت بازی برای پذیرنده
$cuser["userfild"]["rival"] = "$inviterUserId";
$cuser["userfild"]["ingame"] = "on";
$cuser = json_encode($cuser, true);
file_put_contents("data/user/$fromid.json", $cuser);

// تنظیم زمان شروع بازی برای هر دو کاربر
setGameStartTime($inviterUserId);
setGameStartTime($fromid);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "reject_game_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر دعوت‌کننده
$inviterUserId = str_replace("reject_game_", "", $data);

// اطلاع به دعوت‌کننده
jijibot('sendmessage',[
    'chat_id' => $inviterUserId,
    'text' => "❌ دعوت بازی رد شد!

متأسفانه کاربر دعوت بازی شما را رد کرد.",
    'reply_markup' => json_encode([
        'inline_keyboard' => [
            [
                ['text' => "🔙 بازگشت", 'callback_data' => "back"]
            ]
        ]
    ])
]);

// پاسخ به کاربر رد‌کننده
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"❌ دعوت بازی رد شد!

شما دعوت بازی را رد کردید. دعوت‌کننده مطلع شد.",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
            ]
        ]
    ])
]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif ($juser["userfild"]["step"] == 'send_message') {
if($textmassage == true){
// دریافت آیدی کاربر هدف
$targetUserId = $juser["userfild"]["message_target"];

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // بررسی مسدودیت
    if(is_user_blocked($from_id, $targetUserId)) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"🚫 شما توسط این کاربر مسدود شده‌اید و نمی‌توانید پیام ارسال کنید.

کاربر: $targetNickname",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);

        // ریست کردن step
        $juser["userfild"]["step"] = "none";
        unset($juser["userfild"]["message_target"]);
        $juser = json_encode($juser,true);
        file_put_contents("data/user/$from_id.json",$juser);
        return;
    }

    // دریافت اطلاعات فرستنده
    $senderNickname = !empty($juser['userfild']['nickname']) ? $juser['userfild']['nickname'] : "ناشناس";
    $senderToken = $juser['userfild']['user_token'];

    // ارسال پیام با اطلاعات فرستنده به کاربر هدف
    jijibot('sendmessage',[
        'chat_id' => $targetUserId,
        'text' => "💌 پیام دریافتی

شما یک پیام دریافت کرده‌اید:

<blockquote>$textmassage</blockquote>

📤 ارسال شده از: $senderNickname (<a href=\"https://t.me/jdarebot?start=$senderToken\">$senderToken</a>)",
        'parse_mode' => 'HTML',
        'disable_web_page_preview' => true,
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "🔙 بازگشت به منو", 'callback_data' => "back"],
                    ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$from_id"]
                ]
            ]
        ])
    ]);

    // اطلاع به فرستنده
    jijibot('sendmessage',[
        'chat_id' => $chat_id,
        'text' => "✅ پیام شما با موفقیت ارسال شد!

📤 پیام ارسالی:
<blockquote>$textmassage</blockquote>",
        'parse_mode' => 'HTML',
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "🔙 بازگشت به منو", 'callback_data' => "back"]
                ]
            ]
        ])
    ]);

    // ریست کردن step
    $juser["userfild"]["step"] = "none";
    unset($juser["userfild"]["message_target"]);
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);

} else {
    jijibot('sendmessage',[
        'chat_id' => $chat_id,
        'text' => "❌ خطا در ارسال پیام!

کاربر مورد نظر یافت نشد.",
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "🔙 بازگشت به منو", 'callback_data' => "back"]
                ]
            ]
        ])
    ]);

    // ریست کردن step
    $juser["userfild"]["step"] = "none";
    unset($juser["userfild"]["message_target"]);
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);
}
}
else
{
jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"💬 لطفا پیام خود را به صورت متن ارسال کنید!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 لغو",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
}
elseif ($juser["userfild"]["step"] == 'transfer_coins') {
if($textmassage && is_numeric($textmassage)){
// دریافت آیدی کاربر هدف
$targetUserId = $juser["userfild"]["transfer_target"];
$transferAmount = intval($textmassage);

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // بررسی موجودی سکه کاربر فرستنده
    $senderCoins = isset($juser['userfild']['coins']) ? $juser['userfild']['coins'] : 20;

    if($transferAmount < 1) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ حداقل 1 سکه باید انتقال دهید!",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 لغو",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    } elseif($transferAmount > $senderCoins) {
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"❌ موجودی شما کافی نیست! شما $senderCoins سکه دارید.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 لغو",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    } else {
        // انجام انتقال سکه
        $newSenderCoins = $senderCoins - $transferAmount;
        $targetCoins = isset($targetUserData['userfild']['coins']) ? $targetUserData['userfild']['coins'] : 20;
        $newTargetCoins = $targetCoins + $transferAmount;

        // به‌روزرسانی موجودی فرستنده
        $juser['userfild']['coins'] = $newSenderCoins;
        $juser["userfild"]["step"] = "none";
        unset($juser["userfild"]["transfer_target"]);
        $juser = json_encode($juser,true);
        file_put_contents("data/user/$from_id.json",$juser);

        // به‌روزرسانی موجودی گیرنده
        $targetUserData['userfild']['coins'] = $newTargetCoins;
        $targetUserData = json_encode($targetUserData,true);
        file_put_contents($targetUserFile,$targetUserData);

        // اطلاع به فرستنده
        jijibot('sendmessage',[
            'chat_id'=>$chat_id,
            'text'=>"✅ انتقال سکه موفقیت‌آمیز بود!

✱ $transferAmount سکه به کاربر $targetNickname انتقال یافت.
✱ موجودی جدید شما: $newSenderCoins سکه",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 بازگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);

        // اطلاع به گیرنده
        $senderNickname = !empty($juser['userfild']['nickname']) ? $juser['userfild']['nickname'] : "ناشناس";
        $senderToken = isset($juser['userfild']['user_token']) ? $juser['userfild']['user_token'] : "نامشخص";
        jijibot('sendmessage',[
            'chat_id'=>$targetUserId,
            'text'=>"🎁 سکه دریافت کردید!

کاربر $senderNickname (<a href=\"https://t.me/jdarebot?start=$senderToken\">$senderToken</a>) به شما $transferAmount سکه انتقال داده است.

💰 موجودی جدید شما: $newTargetCoins سکه",
            'parse_mode'=>'HTML',
            'disable_web_page_preview'=>true,
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🏠 منوی اصلی",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    }
} else {
    jijibot('sendmessage',[
        'chat_id'=>$chat_id,
        'text'=>"❌ کاربر مورد نظر یافت نشد!",
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🔙 لغو",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);

    // ریست کردن step
    $juser["userfild"]["step"] = "none";
    unset($juser["userfild"]["transfer_target"]);
    $juser = json_encode($juser,true);
    file_put_contents("data/user/$from_id.json",$juser);
}
}
else
{
jijibot('sendmessage',[
    'chat_id'=>$chat_id,
    'text'=>"❌ لطفا یک عدد معتبر وارد کنید!",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 لغو",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
}
elseif(strpos($data, "block_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("block_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد خودش را مسدود کند
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
    return;
}

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // بررسی اینکه آیا کاربر قبلاً مسدود شده
    $blockedUsers = isset($cuser['userfild']['blocked_users']) ? $cuser['userfild']['blocked_users'] : [];

    if(in_array($targetUserId, $blockedUsers)) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "⚠️ این کاربر قبلاً مسدود شده است.",
            'show_alert' => true
        ]);
    } else {
        // اضافه کردن کاربر به لیست مسدودها
        $blockedUsers[] = $targetUserId;
        $cuser['userfild']['blocked_users'] = $blockedUsers;
        $cuser = json_encode($cuser, true);
        file_put_contents("data/user/$fromid.json", $cuser);

        // اطلاع به کاربر مسدود شده
        $blockerNickname = !empty($cuser['userfild']['nickname']) ? $cuser['userfild']['nickname'] : "کاربری";
        jijibot('sendmessage', [
            'chat_id' => $targetUserId,
            'text' => "🚫 اطلاعیه مسدودیت

شما توسط کاربر $blockerNickname مسدود شدید.

<blockquote>از این پس امکان ارسال پیام یا دعوت بازی به این کاربر را نخواهید داشت.</blockquote>",
            'parse_mode' => 'HTML'
        ]);

        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "✅ کاربر $targetNickname با موفقیت مسدود شد.",
            'show_alert' => true
        ]);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "report_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("report_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد خودش را گزارش دهد
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "✅ گزارش شما ارسال شد.",
        'show_alert' => false
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "gift_subscription_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("gift_subscription_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد به خودش اشتراک هدیه دهد
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
    return;
}

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    jijibot('sendmessage',[
        'chat_id'=>$chatid,
        'text'=>"🎁 هدیه اشتراک

شما در حال هدیه دادن اشتراک به کاربر $targetNickname هستید.

<blockquote>جهت هدیه دادن اشتراک، یکی از پلن‌های زیر را انتخاب کنید:</blockquote>",
        'parse_mode'=>'HTML',
        'reply_markup'=>json_encode([
            'inline_keyboard'=>[
                [
                    ['text'=>"🎁 اشتراک 30 روزه : 21هزارتومان",'callback_data'=>"gift_sub_30_$targetUserId"]
                ],
                [
                    ['text'=>"🎁 اشتراک 60 روزه : 42هزارتومان",'callback_data'=>"gift_sub_60_$targetUserId"]
                ],
                [
                    ['text'=>"🎁 اشتراک 90 روزه : 63هزارتومان",'callback_data'=>"gift_sub_90_$targetUserId"]
                ],
                [
                    ['text'=>"🎁 اشتراک 1 ساله : 252هزارتومان",'callback_data'=>"gift_sub_365_$targetUserId"]
                ],
                [
                    ['text'=>"🔙 برگشت",'callback_data'=>"back"]
                ],
            ]
        ])
    ]);
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "gift_sub_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج نوع پلن و آیدی کاربر هدف
$parts = explode("_", $data);
$planType = $parts[1] . "_" . $parts[2]; // مثل sub_30
$targetUserId = $parts[3];

// تعیین نوع پلن و قیمت
$plan_info = array(
    "sub_30" => array("days" => 30, "price" => "21هزارتومان"),
    "sub_60" => array("days" => 60, "price" => "42هزارتومان"),
    "sub_90" => array("days" => 90, "price" => "63هزارتومان"),
    "sub_365" => array("days" => 365, "price" => "252هزارتومان")
);

$selected_plan = $plan_info[$planType];

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // ذخیره اطلاعات هدیه در سشن
    $senderNickname = !empty($cuser['userfild']['nickname']) ? $cuser['userfild']['nickname'] : "کاربری";
    Session::set($fromid, 'gift_plan', $planType);
    Session::set($fromid, 'gift_target_user', $targetUserId);
    Session::set($fromid, 'gift_target_nickname', $targetNickname);
    Session::set($fromid, 'sender_nickname', $senderNickname);
    Session::set($fromid, 'plan_days', $selected_plan['days']);
    Session::set($fromid, 'plan_price', $selected_plan['price']);

    // ذخیره پلن برای callback (مشابه اشتراک عادی)
    Session::set($fromid, 'selected_plan', $planType);

    // بررسی اینکه آیا کاربر قبلاً شماره تلفن تایید شده دارد یا نه
    if(!empty($cuser["userfild"]["phone"])) {
        // کاربر قبلاً شماره تایید کرده - ایجاد لینک پرداخت
        $price = getPlanPrice($planType);
        $amount = convertToRials($price);
        $paymentLink = generatePaymentLink($fromid, $amount, $planType);

        // تعیین متن بر اساس نوع پلن
        $planText = "";
        switch($planType) {
            case 'sub_30':
                $planText = "🎁 هدیه اشتراک 30روزه : 21,000 تومان\n\nشما در حال هدیه دادن اشتراک 30 روزه به کاربر $targetNickname هستید.";
                break;
            case 'sub_60':
                $planText = "🎁 هدیه اشتراک 60روزه : 42,000 تومان\n\nشما در حال هدیه دادن اشتراک 60 روزه به کاربر $targetNickname هستید.";
                break;
            case 'sub_90':
                $planText = "🎁 هدیه اشتراک 90روزه : 63,000 تومان\n\nشما در حال هدیه دادن اشتراک 90 روزه به کاربر $targetNickname هستید.";
                break;
            case 'sub_365':
                $planText = "🎁 هدیه اشتراک 1ساله : 252,000 تومان\n\nشما در حال هدیه دادن اشتراک 1 ساله به کاربر $targetNickname هستید.";
                break;
        }

        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>$planText."\n\n<b>⚠️ برای پرداخت اتصال VPN خود را قطع نمائید.</b>",
            'parse_mode'=>'HTML',
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"💳 پرداخت",'url'=>$paymentLink]
                    ],
                    [
                        ['text'=>"🔙 برگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
    } else {
        // کاربر باید ابتدا شماره تلفن تایید کند
        jijibot('sendmessage',[
            'chat_id'=>$chatid,
            'text'=>"☎️ تایید شماره تلفن

جهت ادامه فرآیند خرید لطفا شماره تلفن خود را با فرمت 09xxxxxxxxx ارسال کنید.

<blockquote>اخذ شماره تلفن به علت جلوگیری از عملیات های کلاهبرداری و فیشینگ است.</blockquote>",
            'parse_mode'=>'HTML',
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 برگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);
        $cuser["userfild"]["step"]="phone_verification";
        $cuser = json_encode($cuser,true);
        file_put_contents("data/user/$fromid.json",$cuser);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "transfer_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("transfer_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد به خودش سکه انتقال دهد
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
    return;
}

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // بررسی موجودی سکه کاربر فرستنده
    $senderCoins = isset($cuser['userfild']['coins']) ? $cuser['userfild']['coins'] : 20;

    if($senderCoins < 1) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ موجودی سکه شما کافی نیست! حداقل 1 سکه نیاز دارید.",
            'show_alert' => true
        ]);
    } else {
        // نمایش صفحه انتقال سکه
        jijibot('sendmessage',[
            'chat_id'=>$chatid,
            'text'=>"🪙 انتقال سکه به $targetNickname

✱ موجودی فعلی شما: $senderCoins سکه

لطفا تعداد سکه‌ای که می‌خواهید انتقال دهید را ارسال کنید:

<blockquote>حداقل 1 سکه و حداکثر $senderCoins سکه می‌توانید انتقال دهید.</blockquote>",
            'parse_mode'=>'HTML',
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🔙 برگشت",'callback_data'=>"back"]
                    ],
                ]
            ])
        ]);

        // ذخیره آیدی کاربر هدف در session برای انتقال سکه
        $cuser["userfild"]["step"] = "transfer_coins";
        $cuser["userfild"]["transfer_target"] = $targetUserId;
        $cuser = json_encode($cuser,true);
        file_put_contents("data/user/$fromid.json",$cuser);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "unblock_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("unblock_", "", $data);

// بررسی اینکه آیا کاربر می‌خواهد خودش را رفع مسدود کند
if($targetUserId == $fromid) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => " ❌ اجرای عملیات روی حساب خود ممکن نیست.",
        'show_alert' => false
    ]);
    return;
}

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUserData = json_decode(file_get_contents($targetUserFile), true);
    $targetNickname = !empty($targetUserData['userfild']['nickname']) ? $targetUserData['userfild']['nickname'] : "ناشناس";

    // دریافت لیست مسدودها
    $blockedUsers = isset($cuser['userfild']['blocked_users']) ? $cuser['userfild']['blocked_users'] : [];

    if(in_array($targetUserId, $blockedUsers)) {
        // حذف کاربر از لیست مسدودها
        $blockedUsers = array_diff($blockedUsers, [$targetUserId]);
        $cuser['userfild']['blocked_users'] = array_values($blockedUsers); // reset array keys
        $cuser = json_encode($cuser, true);
        file_put_contents("data/user/$fromid.json", $cuser);

        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "✅ کاربر $targetNickname از لیست مسدودها حذف شد.",
            'show_alert' => true
        ]);

        // بازگشت به صفحه مسدودها
        $cuser = json_decode(file_get_contents("data/user/$fromid.json"), true);
        $blockedUsers = isset($cuser['userfild']['blocked_users']) ? $cuser['userfild']['blocked_users'] : [];

        if(empty($blockedUsers)) {
            // دیگر کاربری مسدود نیست
            jijibot('editmessagetext',[
                'chat_id'=>$chatid,
                'message_id'=>$messageid,
                'text'=>"🚫 مسدودها

شما هیچ کاربری را مسدود نکرده‌اید.

<blockquote>کاربران مسدود شده قادر به ارسال پیام یا دعوت بازی به شما نخواهند بود.</blockquote>",
                'parse_mode'=>'HTML',
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>[
                        [
                            ['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]
                        ],
                    ]
                ])
            ]);
        } else {
            // نمایش لیست به‌روزرسانی شده
            $blockedList = "🚫 مسدودها\n\nلیست کاربران مسدود شده:\n\n";
            $keyboard = [];

            foreach($blockedUsers as $index => $blockedUserId) {
                $blockedUserFile = "data/user/$blockedUserId.json";
                if(file_exists($blockedUserFile)) {
                    $blockedUserData = json_decode(file_get_contents($blockedUserFile), true);
                    $blockedNickname = !empty($blockedUserData['userfild']['nickname']) ? $blockedUserData['userfild']['nickname'] : "ناشناس";
                    $blockedToken = isset($blockedUserData['userfild']['user_token']) ? $blockedUserData['userfild']['user_token'] : "نامشخص";

                    $blockedList .= "👤 $blockedNickname (<a href=\"https://t.me/jdarebot?start=$blockedToken\">$blockedToken</a>)\n";

                    // اضافه کردن دکمه رفع مسدودیت
                    $keyboard[] = [['text' => "🔓 رفع مسدودیت $blockedNickname", 'callback_data' => "unblock_$blockedUserId"]];
                }
            }

            $blockedList .= "\n<blockquote>برای رفع مسدودیت هر کاربر، روی دکمه مربوطه کلیک کنید.</blockquote>";

            // اضافه کردن دکمه برگشت
            $keyboard[] = [['text'=>"🔙 برگشت",'callback_data'=>"user_settings"]];

            jijibot('editmessagetext',[
                'chat_id'=>$chatid,
                'message_id'=>$messageid,
                'text'=>$blockedList,
                'parse_mode'=>'HTML',
                'disable_web_page_preview'=>true,
                'reply_markup'=>json_encode([
                    'inline_keyboard'=>$keyboard
                ])
            ]);
        }
    } else {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "⚠️ این کاربر در لیست مسدودها شما نیست.",
            'show_alert' => true
        ]);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "view_profile_") === 0) {
if(check_membership($fromid, $channel1, $channel2)){
// استخراج آیدی کاربر هدف از callback_data
$targetUserId = str_replace("view_profile_", "", $data);

// بررسی وجود کاربر هدف
$targetUserFile = "data/user/$targetUserId.json";
if(file_exists($targetUserFile)) {
    $targetUser = json_decode(file_get_contents($targetUserFile), true);

    // دریافت اطلاعات کاربر
    $baseNickname = !empty($targetUser['userfild']['nickname']) ? $targetUser['userfild']['nickname'] : "ناشناس";
    $gender = !empty($targetUser['userfild']['gender']) ? $targetUser['userfild']['gender'] : "تنظیم نشده";
    $city = !empty($targetUser['userfild']['city']) ? $targetUser['userfild']['city'] : "تنظیم نشده";
    $age = !empty($targetUser['userfild']['age']) ? $targetUser['userfild']['age'] : "تنظیم نشده";
    $join_date = isset($targetUser['userfild']['join_date']) ? $targetUser['userfild']['join_date'] : "نامشخص";
    $score = isset($targetUser['userfild']['score']) ? $targetUser['userfild']['score'] : 0;
    $coins = isset($targetUser['userfild']['coins']) ? $targetUser['userfild']['coins'] : 0;

    // بررسی وضعیت VIP
    $isVIP = false;
    if (isset($targetUser['userfild']['subscription']['is_active']) &&
        $targetUser['userfild']['subscription']['is_active'] === true &&
        isset($targetUser['userfild']['subscription']['end_date']) &&
        $targetUser['userfild']['subscription']['end_date'] > time()) {
        $isVIP = true;
    }

    // اضافه کردن ایموجی ✨ برای کاربران VIP
    $nickname = $isVIP ? "✨ " . $baseNickname : $baseNickname;
    $vipStatus = $isVIP ? "\n✱ این کاربر VIP است" : "";

    // دریافت توکن کاربر و آخرین بازدید
    $userToken = isset($targetUser['userfild']['user_token']) ? $targetUser['userfild']['user_token'] : "نامشخص";
    $lastVisit = getLastVisitStatus($targetUser);

    // متن کپشن پروفایل
    $profileCaption = "👤 پروفایل کاربر

✱ نام مستعار : $nickname$vipStatus
✱ توکن کاربر : <a href=\"https://t.me/jdarebot?start=$userToken\">$userToken</a>
✱ آخرین بازدید : $lastVisit
✱ تاریخ ورود : $join_date
✱ جنسیت : $gender
✱ شهر : $city
✱ سن : $age
✱ امتیاز : $score
✱ سکه : $coins";

    // بررسی تصویر ذخیره شده کاربر
    $userPhoto = isset($targetUser['userfild']['photo']) ? $targetUser['userfild']['photo'] : "";

    if (!empty($userPhoto)) {
        // کاربر تصویر ذخیره شده دارد - ارسال تصویر ذخیره شده
        jijibot('sendPhoto', [
            'chat_id' => $chatid,
            'photo' => $userPhoto,
            'caption' => $profileCaption,
            'parse_mode' => 'HTML',
            'reply_markup' => json_encode([
                'inline_keyboard' => [
                    [
                        ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                        ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                    ],
                    [
                        ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                        ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                    ],
                    [
                        ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                        ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                    ],
                    [
                        ['text' => "❌ بستن", 'callback_data' => "close"]
                    ]
                ]
            ])
        ]);
    } else {
        // کاربر تصویر ذخیره شده ندارد - استفاده از تصویر پیش‌فرض
        $defaultPhotoUrl = "https://t.me/fjw2w9c9/1855";

        jijibot('sendPhoto', [
            'chat_id' => $chatid,
            'photo' => $defaultPhotoUrl,
            'caption' => $profileCaption,
            'parse_mode' => 'HTML',
            'reply_markup' => json_encode([
                'inline_keyboard' => [
                    [
                        ['text' => "🎮 دعوت به بازی", 'callback_data' => "invite_$targetUserId"],
                        ['text' => "💬 ارسال پیام", 'callback_data' => "message_$targetUserId"]
                    ],
                    [
                        ['text' => "🚫 مسدود کردن", 'callback_data' => "block_$targetUserId"],
                        ['text' => "🪙 انتقال سکه", 'callback_data' => "transfer_$targetUserId"]
                    ],
                    [
                        ['text' => "⚠️ گزارش", 'callback_data' => "report_$targetUserId"],
                        ['text' => "🎁 هدیه اشتراک", 'callback_data' => "gift_subscription_$targetUserId"]
                    ],
                    [
                        ['text' => "❌ بستن", 'callback_data' => "close"]
                    ]
                ]
            ])
        ]);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ کاربر مورد نظر یافت نشد!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}

elseif($data=="cancel_four_player_search"){
if(check_membership($fromid, $channel1, $channel2)){
// حذف کاربر از صف بازی 4 نفره
removeFromFourPlayerQueue($fromid);

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"❌ جستجو لغو شد!

شما از صف بازی 4 نفره خارج شدید.",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 بازگشت به منو",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}

elseif($data=="cancel_three_player_search"){
if(check_membership($fromid, $channel1, $channel2)){
// حذف کاربر از صف بازی 3 نفره
removeFromThreePlayerQueue($fromid);

jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"❌ جستجو لغو شد!

شما از صف بازی 3 نفره خارج شدید.",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"🔙 بازگشت به منو",'callback_data'=>"back"]
            ],
        ]
    ])
]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}

elseif($data=="leave_four_player_game"){
if(check_membership($fromid, $channel1, $channel2)){
// بررسی اینکه کاربر در بازی 4 نفره است
if(isUserInFourPlayerGame($fromid)) {
    $gameId = getUserFourPlayerGameId($fromid);
    $gameData = getFourPlayerGameData($gameId);

    if($gameData) {
        // دریافت نام کاربر خارج شونده
        $leavingPlayerData = json_decode(file_get_contents("data/user/$fromid.json"), true);
        $leavingPlayerNickname = !empty($leavingPlayerData['userfild']['nickname']) ? $leavingPlayerData['userfild']['nickname'] : "ناشناس";

        // بررسی زمان بازی برای بازگرداندن سکه
        $shouldRefundCoins = !isGameLongerThan2Minutes($fromid);

        // ارسال پیام به همه بازیکنان
        foreach($gameData['players'] as $playerId) {
            // پیام اول: بستن دکمه‌ها + اطلاع‌رسانی سکه
            if ($shouldRefundCoins) {
                if (!hasActiveSubscription($playerId)) {
                    refundCoins($playerId, 2);
                    $coinMessage = "💳 سکه

به علت پایان 2 سکه مصرف شده در این بازی به حساب شما برگردانده شد.";
                } else {
                    $coinMessage = "💳 سکه

به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشده بود.";
                }
            } else {
                $coinMessage = "💳 سکه

به دلیل اینکه بازی بیشتر از 2 دقیقه طول کشیده است، سکه‌ها بازگردانده نمی‌شوند.";
            }

            jijibot('sendmessage',[
                'chat_id'=>$playerId,
                'text'=>$coinMessage,
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);

            // پیام دوم: اعلام پایان بازی + دکمه منوی اصلی
            if($playerId == $fromid) {
                // پیام برای کاربر خارج شونده
                jijibot('sendmessage',[
                    'chat_id'=>$playerId,
                    'text'=>"🚪 شما از بازی 4 نفره خارج شدید!

بازی برای همه بازیکنان پایان یافت.

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                            ],
                        ]
                    ])
                ]);
            } else {
                // پیام برای سایر بازیکنان
                jijibot('sendmessage',[
                    'chat_id'=>$playerId,
                    'text'=>"🚪 بازی 4 نفره به دلیل خروج $leavingPlayerNickname پایان یافت!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                            ],
                        ]
                    ])
                ]);
            }
        }

        // پایان بازی
        endFourPlayerGame($gameId, 'player_left');

        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"✅ شما از بازی 4 نفره خارج شدید!

بازی برای همه بازیکنان پایان یافت.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                    ],
                ]
            ])
        ]);
    } else {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ اطلاعات بازی یافت نشد!",
            'show_alert' => true
        ]);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ شما در بازی 4 نفره نیستید!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}

elseif($data=="leave_three_player_game"){
if(check_membership($fromid, $channel1, $channel2)){
// بررسی اینکه کاربر در بازی 3 نفره است
if(isUserInThreePlayerGame($fromid)) {
    $gameId = getUserThreePlayerGameId($fromid);
    $gameData = getThreePlayerGameData($gameId);

    if($gameData) {
        // دریافت نام کاربر خارج شونده
        $leavingPlayerData = json_decode(file_get_contents("data/user/$fromid.json"), true);
        $leavingPlayerNickname = !empty($leavingPlayerData['userfild']['nickname']) ? $leavingPlayerData['userfild']['nickname'] : "ناشناس";

        // بررسی زمان بازی برای بازگرداندن سکه
        $shouldRefundCoins = !isGameLongerThan2Minutes($fromid);

        // ارسال پیام به همه بازیکنان
        foreach($gameData['players'] as $playerId) {
            // پیام اول: بستن دکمه‌ها + اطلاع‌رسانی سکه
            if ($shouldRefundCoins) {
                if (!hasActiveSubscription($playerId)) {
                    refundCoins($playerId, 2);
                    $coinMessage = "💳 سکه

به علت پایان 2 سکه مصرف شده در این بازی به حساب شما برگردانده شد.";
                } else {
                    $coinMessage = "💳 سکه

به دلیل داشتن اشتراک ویژه، سکه‌ای از شما کسر نشده بود.";
                }
            } else {
                $coinMessage = "💳 سکه

به دلیل اینکه بازی بیشتر از 2 دقیقه طول کشیده است، سکه‌ها بازگردانده نمی‌شوند.";
            }

            jijibot('sendmessage',[
                'chat_id'=>$playerId,
                'text'=>$coinMessage,
                'reply_markup'=>json_encode([
                    'remove_keyboard'=>true
                ])
            ]);

            // پیام دوم: اعلام پایان بازی + دکمه منوی اصلی
            if($playerId == $fromid) {
                // پیام برای کاربر خارج شونده
                jijibot('sendmessage',[
                    'chat_id'=>$playerId,
                    'text'=>"🚪 شما از بازی 3 نفره خارج شدید!

بازی برای همه بازیکنان پایان یافت.

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                            ],
                        ]
                    ])
                ]);
            } else {
                // پیام برای سایر بازیکنان
                jijibot('sendmessage',[
                    'chat_id'=>$playerId,
                    'text'=>"🚪 بازی 3 نفره به دلیل خروج $leavingPlayerNickname پایان یافت!

شما میتوانید از طریق دکمه زیر به منوی اصلی بازگردید.",
                    'reply_markup'=>json_encode([
                        'inline_keyboard'=>[
                            [
                                ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                            ],
                        ]
                    ])
                ]);
            }
        }

        // پایان بازی
        endThreePlayerGame($gameId, 'player_left');

        jijibot('editmessagetext',[
            'chat_id'=>$chatid,
            'message_id'=>$messageid,
            'text'=>"✅ شما از بازی 3 نفره خارج شدید!

بازی برای همه بازیکنان پایان یافت.",
            'reply_markup'=>json_encode([
                'inline_keyboard'=>[
                    [
                        ['text'=>"🏠 منوی اصلی",'callback_data'=>"main_menu"]
                    ],
                ]
            ])
        ]);
    } else {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ اطلاعات بازی یافت نشد!",
            'show_alert' => true
        ]);
    }
} else {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ شما در بازی 3 نفره نیستید!",
        'show_alert' => true
    ]);
}
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="cancel_leave_game"){
if(check_membership($fromid, $channel1, $channel2)){
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ خروج لغو شد!

به بازی ادامه دهید.",
    'reply_markup'=>json_encode([
        'remove_keyboard'=>true
    ])
]);

// ارسال پیام جدید با دکمه‌های بازی
jijibot('sendmessage',[
    'chat_id'=>$chatid,
    'text'=>"🎮 بازی 3 نفره در حال انجام

از دکمه‌های زیر استفاده کنید:",
    'reply_markup'=>json_encode([
        'keyboard'=>[
            [
                ['text'=>"👥 مشاهده بازیکنان"],
                ['text'=>"❌ ترک بازی"]
            ],
        ],
        'resize_keyboard'=>true
    ])
]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "four_dare_") === 0 || strpos($data, "four_truth_") === 0){
if(check_membership($fromid, $channel1, $channel2)){
// استخراج game ID از callback data
$gameId = str_replace(["four_dare_", "four_truth_"], "", $data);
$choice = strpos($data, "four_dare_") === 0 ? "dare" : "truth";

// بررسی وجود بازی
$gameData = getFourPlayerGameData($gameId);
if (!$gameData) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ بازی یافت نشد!",
        'show_alert' => true
    ]);
    return;
}

// بررسی اینکه کاربر در این بازی است
if (!in_array($fromid, $gameData['players'])) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ شما در این بازی نیستید!",
        'show_alert' => true
    ]);
    return;
}

// بررسی اینکه کاربر پاسخ‌دهنده است (فقط او می‌تواند انتخاب کند)
if ($fromid != $gameData['current_answerer']) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ فقط پاسخ‌دهنده می‌تواند انتخاب کند!",
        'show_alert' => true
    ]);
    return;
}

// بررسی وضعیت بازی
if ($gameData['game_state'] != 'waiting_for_choice') {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ زمان انتخاب گذشته است!",
        'show_alert' => true
    ]);
    return;
}

$choiceText = $choice == "dare" ? "جرعت" : "حقیقت";

// به‌روزرسانی وضعیت بازی
$gameData['current_choice'] = $choice;
$gameData['game_state'] = 'waiting_for_question';
updateFourPlayerGameData($gameId, $gameData);

// دریافت اطلاعات بازیکنان
$playerNicknames = [];
foreach ($gameData['players'] as $playerId) {
    $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
    $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
        $playerData['userfild']['nickname'] : "ناشناس";
}

$answererNickname = $playerNicknames[$gameData['current_answerer']];
$questionerNickname = $playerNicknames[$gameData['current_questioner']];
$observer1Nickname = $playerNicknames[$gameData['current_observer1']];
$observer2Nickname = $playerNicknames[$gameData['current_observer2']];

// پیام تایید برای پاسخ‌دهنده - edit کردن پیام انتخاب
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ انتخاب شما ثبت شد!

💪🏻 شما $choiceText را انتخاب کردید.

⏳ منتظر سوال $questionerNickname باشید...

🎯 نقش شما: پاسخ‌دهنده
👤 سوال‌کننده: $questionerNickname
👁 مشاهده‌گر 1: $observer1Nickname
👁 مشاهده‌گر 2: $observer2Nickname",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"👥 مشاهده بازیکنان",'callback_data'=>"show_four_player_status"]
            ],
        ]
    ])
]);

// اطلاع‌رسانی به سوال‌کننده با دکمه‌های انتخاب سوال
$choiceTypeText = $choice == "dare" ? "جرعت" : "حقیقت";
$choiceEmoji = $choice == "dare" ? "💪🏻" : "📘";

jijibot('sendmessage',[
    'chat_id' => $gameData['current_questioner'],
    'text' => "$choiceEmoji $choiceTypeText

✱ $answererNickname $choiceText را انتخاب کرد.
✱ شما می‌توانید از دکمه‌های زیر سوال آماده انتخاب کنید یا سوال دلخواه خود را تایپ کنید:

🎯 نقش شما: سوال‌کننده
👤 پاسخ‌دهنده: $answererNickname
👁 مشاهده‌گر 1: $observer1Nickname
👁 مشاهده‌گر 2: $observer2Nickname",
    'reply_markup' => json_encode([
        'inline_keyboard' => [
            [
                ['text' => "$choiceEmoji $choiceTypeText", 'callback_data' => "four_question_normal_{$choice}_$gameId"],
                ['text' => "$choiceEmoji $choiceTypeText +18", 'callback_data' => "four_question_plus18_{$choice}_$gameId"]
            ],
            [
                ['text' => "👥 مشاهده بازیکنان", 'callback_data' => "show_four_player_status"],
                ['text' => "❌ ترک بازی", 'callback_data' => "leave_four_player_game_$gameId"]
            ],
        ]
    ])
]);

// اطلاع‌رسانی به مشاهده‌گر 1
jijibot('sendmessage',[
    'chat_id' => $gameData['current_observer1'],
    'text' => "👀 به‌روزرسانی بازی

💪🏻 $answererNickname $choiceText را انتخاب کرد.

⏳ منتظر سوال $questionerNickname باشید...

🎯 نقش شما: مشاهده‌گر 1
👤 سوال‌کننده: $questionerNickname
👤 پاسخ‌دهنده: $answererNickname
👁 مشاهده‌گر 2: $observer2Nickname

💬 توجه: دیگر نمی‌توانید با سوال‌کننده چت کنید (فقط مشاهده کنید)",
    'reply_markup' => json_encode([
        'keyboard' => [
            [
                ['text' => "👥 مشاهده بازیکنان"],
                ['text' => "❌ ترک بازی"]
            ],
        ],
        'resize_keyboard' => true
    ])
]);

// اطلاع‌رسانی به مشاهده‌گر 2
jijibot('sendmessage',[
    'chat_id' => $gameData['current_observer2'],
    'text' => "👀 به‌روزرسانی بازی

💪🏻 $answererNickname $choiceText را انتخاب کرد.

⏳ منتظر سوال $questionerNickname باشید...

🎯 نقش شما: مشاهده‌گر 2
👤 سوال‌کننده: $questionerNickname
👤 پاسخ‌دهنده: $answererNickname
👁 مشاهده‌گر 1: $observer1Nickname

💬 توجه: دیگر نمی‌توانید با سوال‌کننده چت کنید (فقط مشاهده کنید)",
    'reply_markup' => json_encode([
        'keyboard' => [
            [
                ['text' => "👥 مشاهده بازیکنان"],
                ['text' => "❌ ترک بازی"]
            ],
        ],
        'resize_keyboard' => true
    ])
]);

// تنظیم step برای سوال‌کننده
$questionerData = json_decode(file_get_contents("data/user/{$gameData['current_questioner']}.json"), true);
$questionerData['userfild']['step'] = 'four_player_question';
$questionerData['userfild']['four_player_game_id'] = $gameId;
file_put_contents("data/user/{$gameData['current_questioner']}.json", json_encode($questionerData, true));
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "three_dare_") === 0 || strpos($data, "three_truth_") === 0){
if(check_membership($fromid, $channel1, $channel2)){
// استخراج game ID از callback data
$gameId = str_replace(["three_dare_", "three_truth_"], "", $data);
$choice = strpos($data, "three_dare_") === 0 ? "dare" : "truth";

// بررسی وجود بازی
$gameData = getThreePlayerGameData($gameId);
if (!$gameData) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ بازی یافت نشد!",
        'show_alert' => true
    ]);
    return;
}

// بررسی اینکه کاربر در این بازی است
if (!in_array($fromid, $gameData['players'])) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ شما در این بازی نیستید!",
        'show_alert' => true
    ]);
    return;
}

// بررسی اینکه کاربر پاسخ‌دهنده است (فقط او می‌تواند انتخاب کند)
if ($fromid != $gameData['current_answerer']) {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ فقط پاسخ‌دهنده می‌تواند انتخاب کند!",
        'show_alert' => true
    ]);
    return;
}

// بررسی وضعیت بازی
if ($gameData['game_state'] != 'waiting_for_choice') {
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "❌ زمان انتخاب گذشته است!",
        'show_alert' => true
    ]);
    return;
}

$choiceText = $choice == "dare" ? "جرعت" : "حقیقت";

// به‌روزرسانی وضعیت بازی
$gameData['current_choice'] = $choice;
$gameData['game_state'] = 'waiting_for_question';
updateThreePlayerGameData($gameId, $gameData);

// دریافت اطلاعات بازیکنان
$playerNicknames = [];
foreach ($gameData['players'] as $playerId) {
    $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
    $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
        $playerData['userfild']['nickname'] : "ناشناس";
}

$answererNickname = $playerNicknames[$gameData['current_answerer']];
$questionerNickname = $playerNicknames[$gameData['current_questioner']];
$observerNickname = $playerNicknames[$gameData['current_observer']];

// پیام تایید برای پاسخ‌دهنده - edit کردن پیام انتخاب
jijibot('editmessagetext',[
    'chat_id'=>$chatid,
    'message_id'=>$messageid,
    'text'=>"✅ انتخاب شما ثبت شد!

💪🏻 شما $choiceText را انتخاب کردید.

⏳ منتظر سوال $questionerNickname باشید...

🎯 نقش شما: پاسخ‌دهنده
👤 سوال‌کننده: $questionerNickname
👁 مشاهده‌گر: $observerNickname",
    'reply_markup'=>json_encode([
        'inline_keyboard'=>[
            [
                ['text'=>"👥 مشاهده بازیکنان",'callback_data'=>"show_three_player_status"]
            ],
        ]
    ])
]);

// اطلاع‌رسانی به سوال‌کننده با دکمه‌های انتخاب سوال
$choiceTypeText = $choice == "dare" ? "جرعت" : "حقیقت";
$choiceEmoji = $choice == "dare" ? "💪🏻" : "📘";

jijibot('sendmessage',[
    'chat_id' => $gameData['current_questioner'],
    'text' => "$choiceEmoji $choiceTypeText

✱ $answererNickname $choiceText را انتخاب کرد.
✱ شما می‌توانید از دکمه‌های زیر سوال آماده انتخاب کنید یا سوال دلخواه خود را تایپ کنید:

🎯 نقش شما: سوال‌کننده
👤 پاسخ‌دهنده: $answererNickname
👁 مشاهده‌گر: $observerNickname",
    'reply_markup' => json_encode([
        'inline_keyboard' => [
            [
                ['text' => "$choiceEmoji $choiceTypeText", 'callback_data' => "three_question_normal_{$choice}_$gameId"],
                ['text' => "$choiceEmoji $choiceTypeText +18", 'callback_data' => "three_question_plus18_{$choice}_$gameId"]
            ],
            [
                ['text' => "👥 مشاهده بازیکنان", 'callback_data' => "show_three_player_status"],
                ['text' => "❌ ترک بازی", 'callback_data' => "leave_three_player_game_$gameId"]
            ],
        ]
    ])
]);

// اطلاع‌رسانی به مشاهده‌گر
jijibot('sendmessage',[
    'chat_id' => $gameData['current_observer'],
    'text' => "👀 به‌روزرسانی بازی

💪🏻 $answererNickname $choiceText را انتخاب کرد.

⏳ منتظر سوال $questionerNickname باشید...

🎯 نقش شما: مشاهده‌گر
👤 سوال‌کننده: $questionerNickname
👤 پاسخ‌دهنده: $answererNickname

💬 توجه: دیگر نمی‌توانید با سوال‌کننده چت کنید (فقط مشاهده کنید)",
    'reply_markup' => json_encode([
        'keyboard' => [
            [
                ['text' => "👥 مشاهده بازیکنان"],
                ['text' => "❌ ترک بازی"]
            ],
        ],
        'resize_keyboard' => true
    ])
]);

// تنظیم step برای سوال‌کننده
$questionerData = json_decode(file_get_contents("data/user/{$gameData['current_questioner']}.json"), true);
$questionerData['userfild']['step'] = 'three_player_question';
$questionerData['userfild']['three_player_game_id'] = $gameId;
file_put_contents("data/user/{$gameData['current_questioner']}.json", json_encode($questionerData, true));
}
elseif($data=="show_four_player_status"){
if(check_membership($fromid, $channel1, $channel2)){
    // نمایش وضعیت بازی 4 نفره
    showFourPlayerGameStatus($fromid);

    // پاسخ به callback query
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "✅ وضعیت بازی نمایش داده شد",
        'show_alert' => false
    ]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif($data=="show_three_player_status"){
if(check_membership($fromid, $channel1, $channel2)){
    // نمایش وضعیت بازی 3 نفره
    showThreePlayerGameStatus($fromid);

    // پاسخ به callback query
    jijibot('answercallbackquery', [
        'callback_query_id' => $membercall,
        'text' => "✅ وضعیت بازی نمایش داده شد",
        'show_alert' => false
    ]);
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "four_question_normal_") === 0 || strpos($data, "four_question_plus18_") === 0){
if(check_membership($fromid, $channel1, $channel2)){
    // استخراج اطلاعات از callback data
    $isPlus18 = strpos($data, "four_question_plus18_") === 0;
    $prefix = $isPlus18 ? "four_question_plus18_" : "four_question_normal_";
    $remaining = str_replace($prefix, "", $data);

    // استخراج choice و gameId
    $parts = explode("_", $remaining);
    $choice = $parts[0]; // dare یا truth
    $gameId = implode("_", array_slice($parts, 1)); // باقی قسمت‌ها gameId هستند

    // بررسی وجود بازی
    $gameData = getFourPlayerGameData($gameId);
    if (!$gameData) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ بازی یافت نشد!",
            'show_alert' => true
        ]);
        return;
    }

    // بررسی اینکه کاربر سوال‌کننده است
    if ($fromid != $gameData['current_questioner']) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ فقط سوال‌کننده می‌تواند سوال انتخاب کند!",
            'show_alert' => true
        ]);
        return;
    }

    // دریافت سوال تصادفی
    if ($isPlus18) {
        // دریافت جنسیت پاسخ‌دهنده برای سوال +18
        $answererData = json_decode(file_get_contents("data/user/{$gameData['current_answerer']}.json"), true);
        $answererGender = isset($answererData['userfild']['gender']) ?
            ($answererData['userfild']['gender'] == 'دختر' ? 'girl' : 'boy') : null;

        if ($choice == "dare") {
            $question = getRandomDareChallenge('plus18', $answererGender);
        } else {
            $question = getRandomTruthQuestion('plus18', $answererGender);
        }
    } else {
        if ($choice == "dare") {
            $question = getRandomDareChallenge('normal');
        } else {
            $question = getRandomTruthQuestion('normal');
        }
    }

    // ارسال سوال به پاسخ‌دهنده
    $choiceEmoji = $choice == "dare" ? "💪🏻" : "📘";
    $choiceText = $choice == "dare" ? "جرعت" : "حقیقت";
    $typeText = $isPlus18 ? " +18" : "";

    jijibot('sendmessage',[
        'chat_id' => $gameData['current_answerer'],
        'text' => "$choiceEmoji سوال $choiceText$typeText:

📝 $question

⏰ شما 5 دقیقه فرصت دارید برای پاسخ دادن.",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // اطلاع‌رسانی به سوال‌کننده
    jijibot('editmessagetext',[
        'chat_id' => $chatid,
        'message_id' => $messageid,
        'text' => "✅ سوال$typeText ارسال شد!

📝 سوال شما: $question

⏳ منتظر پاسخ بمانید...",
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان", 'callback_data' => "show_four_player_status"]
                ],
            ]
        ])
    ]);

    // اطلاع‌رسانی به مشاهده‌گران
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $answererNickname = $playerNicknames[$gameData['current_answerer']];
    $questionerNickname = $playerNicknames[$gameData['current_questioner']];

    // اطلاع‌رسانی به مشاهده‌گر 1
    jijibot('sendmessage',[
        'chat_id' => $gameData['current_observer1'],
        'text' => "👀 سوال$typeText ارسال شد

📝 سوال $questionerNickname: $question

⏳ منتظر پاسخ $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // اطلاع‌رسانی به مشاهده‌گر 2
    jijibot('sendmessage',[
        'chat_id' => $gameData['current_observer2'],
        'text' => "👀 سوال$typeText ارسال شد

📝 سوال $questionerNickname: $question

⏳ منتظر پاسخ $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // به‌روزرسانی وضعیت بازی
    $gameData['current_question'] = $question;
    $gameData['game_state'] = 'waiting_for_answer';
    updateFourPlayerGameData($gameId, $gameData);

    // تنظیم step برای پاسخ‌دهنده
    $answererData = json_decode(file_get_contents("data/user/{$gameData['current_answerer']}.json"), true);
    $answererData['userfild']['step'] = 'four_player_answer';
    $answererData['userfild']['four_player_game_id'] = $gameId;
    file_put_contents("data/user/{$gameData['current_answerer']}.json", json_encode($answererData, true));
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
elseif(strpos($data, "three_question_normal_") === 0 || strpos($data, "three_question_plus18_") === 0){
if(check_membership($fromid, $channel1, $channel2)){
    // استخراج اطلاعات از callback data
    $isPlus18 = strpos($data, "three_question_plus18_") === 0;
    $prefix = $isPlus18 ? "three_question_plus18_" : "three_question_normal_";
    $remaining = str_replace($prefix, "", $data);

    // استخراج choice و gameId
    $parts = explode("_", $remaining);
    $choice = $parts[0]; // dare یا truth
    $gameId = implode("_", array_slice($parts, 1)); // باقی قسمت‌ها gameId هستند

    // بررسی وجود بازی
    $gameData = getThreePlayerGameData($gameId);
    if (!$gameData) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ بازی یافت نشد!",
            'show_alert' => true
        ]);
        return;
    }

    // بررسی اینکه کاربر سوال‌کننده است
    if ($fromid != $gameData['current_questioner']) {
        jijibot('answercallbackquery', [
            'callback_query_id' => $membercall,
            'text' => "❌ فقط سوال‌کننده می‌تواند سوال انتخاب کند!",
            'show_alert' => true
        ]);
        return;
    }

    // دریافت سوال تصادفی
    if ($isPlus18) {
        // دریافت جنسیت پاسخ‌دهنده برای سوال +18
        $answererData = json_decode(file_get_contents("data/user/{$gameData['current_answerer']}.json"), true);
        $answererGender = isset($answererData['userfild']['gender']) ?
            ($answererData['userfild']['gender'] == 'دختر' ? 'girl' : 'boy') : null;

        if ($choice == "dare") {
            $question = getRandomDareChallenge('plus18', $answererGender);
        } else {
            $question = getRandomTruthQuestion('plus18', $answererGender);
        }
    } else {
        if ($choice == "dare") {
            $question = getRandomDareChallenge('normal');
        } else {
            $question = getRandomTruthQuestion('normal');
        }
    }

    // ارسال سوال به پاسخ‌دهنده
    $choiceEmoji = $choice == "dare" ? "💪🏻" : "📘";
    $choiceText = $choice == "dare" ? "جرعت" : "حقیقت";
    $typeText = $isPlus18 ? " +18" : "";

    jijibot('sendmessage',[
        'chat_id' => $gameData['current_answerer'],
        'text' => "$choiceEmoji سوال $choiceText$typeText:

📝 $question

⏰ شما 5 دقیقه فرصت دارید برای پاسخ دادن.",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // اطلاع‌رسانی به سوال‌کننده
    jijibot('editmessagetext',[
        'chat_id' => $chatid,
        'message_id' => $messageid,
        'text' => "✅ سوال$typeText ارسال شد!

📝 سوال شما: $question

⏳ منتظر پاسخ بمانید...",
        'reply_markup' => json_encode([
            'inline_keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان", 'callback_data' => "show_three_player_status"]
                ],
            ]
        ])
    ]);

    // اطلاع‌رسانی به مشاهده‌گر
    $playerNicknames = [];
    foreach ($gameData['players'] as $playerId) {
        $playerData = json_decode(file_get_contents("data/user/$playerId.json"), true);
        $playerNicknames[$playerId] = !empty($playerData['userfild']['nickname']) ?
            $playerData['userfild']['nickname'] : "ناشناس";
    }

    $answererNickname = $playerNicknames[$gameData['current_answerer']];
    $questionerNickname = $playerNicknames[$gameData['current_questioner']];

    jijibot('sendmessage',[
        'chat_id' => $gameData['current_observer'],
        'text' => "👀 سوال$typeText ارسال شد

📝 سوال $questionerNickname: $question

⏳ منتظر پاسخ $answererNickname باشید...",
        'reply_markup' => json_encode([
            'keyboard' => [
                [
                    ['text' => "👥 مشاهده بازیکنان"],
                    ['text' => "❌ ترک بازی"]
                ],
            ],
            'resize_keyboard' => true
        ])
    ]);

    // به‌روزرسانی وضعیت بازی
    $gameData['current_question'] = $question;
    $gameData['game_state'] = 'waiting_for_answer';
    updateThreePlayerGameData($gameId, $gameData);

    // تنظیم step برای پاسخ‌دهنده
    $answererData = json_decode(file_get_contents("data/user/{$gameData['current_answerer']}.json"), true);
    $answererData['userfild']['step'] = 'three_player_answer';
    $answererData['userfild']['three_player_game_id'] = $gameId;
    file_put_contents("data/user/{$gameData['current_answerer']}.json", json_encode($answererData, true));
}
else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}



else
{
force_join_edit($chatid, $messageid, $firstname, $usernamebot);
}
}
?>